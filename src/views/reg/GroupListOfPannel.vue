<template>
  <div>
    <!--    <a-alert v-if="!customerRegId" message="请选择体检人" type="error" :show-icon="true" :closable="false" style="margin-bottom: 10px" />-->
    <div style="height: 60vh; display: flex; justify-content: center; align-items: center" v-if="!customerRegId">
      <a-empty :image="simpleImage" description="项目管理面板" />
    </div>
    <a-card v-if="customerRegId" size="small" title="项目列表">
      <template #extra>
        <a-space size="small">
          <a-button size="small" type="primary" @click="removeRegGroupBatch" :disabled="!removeBtnEnable || !allowModifyItems">删除</a-button>
          <a-button size="small" type="primary" @click="minusRegGroupBatch" :disabled="!minusBtnEnable || !allowModifyItems">减项</a-button>
          <a-button size="small" type="primary" @click="undoMinusRegGroupBatch" :disabled="!undoMinusBtnEnable || !allowModifyItems">反减</a-button>
          <a-button
            size="small"
            type="primary"
            @click="refundFeeBatch"
            :disabled="!refundEnable || !allowModifyItems"
            v-if="hasPermission('fee:customer_reg_recipe:refund')"
          >退费</a-button
          >
          <!--          <template v-if="companyTeam?.id && props.showPrice">
            <a-button size="small" type="primary" @click="setPayerType('单位支付')">单位支付</a-button>
            <a-button size="small" type="primary" @click="setPayerType('个人支付')">个人支付</a-button>
          </template>-->
          <a-divider type="vertical" />
          <a-button type="primary" size="middle" @click="openGroupPannel" :disabled="!allowModifyItems"><ProfileOutlined />项目管理</a-button>
        </a-space>
      </template>

      <!-- 依赖项目提示区域 -->
      <div v-if="missingDependencies.length > 0" class="missing-dependencies-alert" style="margin-bottom: 16px">
        <a-alert type="warning" show-icon :closable="false">
          <template #message>
            <div class="missing-dependencies-content">
              <span class="alert-title">检测到缺失的依赖项目</span>
              <div class="missing-projects-list">
                <a-tag
                  v-for="dependency in missingDependencies"
                  :key="dependency.dependentId"
                  color="orange"
                  style="margin: 2px 4px 2px 0"
                  :title="`依赖此项目的检查项目: ${dependency.relatedItemsText}`"
                >
                  {{ dependency.dependentName }}
                  <template v-if="dependency.dependentItemDetails"> ({{ dependency.dependentItemDetails }}) </template>
                </a-tag>
              </div>
            </div>
          </template>
          <template #action>
            <a-button type="primary" size="small" @click="handleQuickAddAllDependencies" :loading="addingDependencies"> 一键添加 </a-button>
          </template>
        </a-alert>
      </div>
      <div style="display: flex; justify-content: space-between; align-items: center">
        <div style="display: flex; align-items: center">
          <template v-if="props.showPrice">
            <span style="font-size: 14px">总价:</span><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPrice }}</span>
            <a-divider type="vertical" />
            <span style="font-size: 14px">折后:</span><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPriceAfterDis }}</span>
          </template>
          <!-- <a-divider type="vertical" />
                       <span style="font-size: 14px">单位付:</span
            ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalCompanyPayAmount }}</span>
            <a-divider type="vertical" />
            <span style="font-size: 14px">个人付:</span
            ><span style="color: red; font-weight: bold; font-size: 16px">￥{{ totalPersonPayAmount }}</span>-->
          <template v-if="(companyTeam.limitAmount || customerReg.originCustomerLimitAmountId) && props.showPrice">
            <a-divider type="vertical" />
            <span type="danger" style="font-size: 14px">额度:</span
            ><!--<span style="color: red; font-weight: bold; font-size: 16px">￥{{ remainingBalance }}</span>
            <span style="font-weight: bold; font-size: 16px">(总{{ customerReg.limitAmount }})</span>-->
            <span style="color: red; font-weight: bold; font-size: 16px">￥{{ remainingBalance }}</span>
            <span style="font-size: 12px">({{ limitDetailDesc }})</span>
            <!--            <span style="font-weight: bold; font-size: 16px">({{ customerReg.limitAmount }})</span>-->
            <a @click="openRelatedLimitOperationRecordListModal" style="margin-left: 5px">记录</a>
            <template v-if="companyTeam.allowTransferAnother == '1' || companyTeam.allowTransferCard == '1'">
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link" @click.prevent>更多 <DownOutlined /></a>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-if="hasPermission('fee:limit_operation_record:transfer2Another') && companyTeam.allowTransferAnother == '1'">
                      <a @click="openLimitOperationModal('转让')">转让</a>
                    </a-menu-item>
                    <a-menu-item v-if="hasPermission('fee:limit_operation_record:transfer2Card') && companyTeam.allowTransferCard == '1'">
                      <a @click="openLimitOperationModal('转储')">转储</a>
                    </a-menu-item>
                    <a-menu-item v-if="hasPermission('fee:limit_operation_record:subtractLimitAmount')">
                      <a @click="openLimitOperationModal('扣减')">扣减</a>
                    </a-menu-item>
                    <a-menu-item>
                      <a @click="showLimitOperationRecord">操作记录</a>
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </template>
          </template>
        </div>
        <a-space>
          <a-button
            size="small"
            type="primary"
            @click="handleSendGroup2His"
            :disabled="customerReg.status != '已登记' || !allowModifyItems"
            v-if="sendItemGroup2HisFlag == '1'"
          >推送项目</a-button
          >
          <a-button size="small" type="primary" @click="handleAddItemDone" :disabled="customerReg.status != '已登记' || !allowModifyItems"
          >完成加项</a-button
          >
          <a-dropdown>
            <a class="ant-dropdown-link" @click.prevent>
              <MenuOutlined />
            </a>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <a @click="showCheckStatus">检查状态</a>
                </a-menu-item>
                <a-menu-item :disabled="!allowModifyItems">
                  <a @click="allowModifyItems && openHistoryRegListModal()">使用历史体检项目</a>
                </a-menu-item>
                <a-menu-item>
                  <a @click="showColorModal">表格背景</a>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
      <a-flex justify="left" align="center" />

      <a-alert message="当前总检状态是审核通过状态，项目列表已禁止修改" type="warning" v-if="!allowModifyItems" style="margin-bottom: 5px" />

      <a-alert
        :message="mustCheckItemTip"
        :type="mustCheckItemGroupFiltered.length > 0 ? 'warning' : 'success'"
        v-if="customerReg?.riskFactor"
        style="margin-bottom: 5px"
      >
        <template #action>
          <a-space
          ><a-button size="small" type="text" @click="addMustCheckItem" :disabled="!allowModifyItems">添加</a-button>
            <a-button size="small" type="text" @click="openMustCheckItemModal">查看</a-button></a-space
          >
        </template>
      </a-alert>
      <a-form layout="inline" :model="batchFormState" style="margin-bottom: 10px">
        <a-row style="width: 100%" :gutter="8">
          <a-col :span="14">
            <a-select
              ref="quickSearchSelectRef"
              v-model:value="quikSearchState.value"
              label-in-value
              placeholder="快速搜索并添加项目，输入--符号搜索套餐"
              style="width: 100%"
              :filter-option="false"
              :not-found-content="quikSearchState.fetching ? undefined : null"
              :options="quikSearchState.data"
              :showSearch="true"
              @search="fetchItemGroup"
              @change="handleSearchChange"
              @keydown="handleQuickSearchKeydown"
              :disabled="!allowModifyItems"
            >
              <template v-if="quikSearchState.fetching" #notFoundContent>
                <a-spin size="small" />
              </template>
            </a-select>
          </a-col>
          <a-col :span="7">
            <a-select
              ref="partSelectRef"
              v-model:value="partSearchState.selectedPart"
              placeholder="选择部位，点击快速添加（支持拼音缩写搜索）"
              style="width: 100%"
              :disabled="!partSearchState.currentProject || !allowModifyItems"
              :loading="partSearchState.loading"
              :options="partSearchState.options"

              @focus="handlePartSelectFocus"
              @change="handlePartSelectChange"
              allow-clear
              show-search
              :filter-option="filterPartOption"
            >
              <template v-if="partSearchState.loading" #suffixIcon>
                <a-spin size="small" />
              </template>
            </a-select>
          </a-col>
          <a-col :span="3">
            <a-button
              type="primary"
              size="middle"
              @click="handleQuickAdd"
              :disabled="!partSearchState.currentProject || !allowModifyItems || regGroupLoading"
              :loading="partSearchState.adding || regGroupLoading"
              style="width: 100%"
              title="快捷添加项目"
            >
              添加
            </a-button>
          </a-col>
        </a-row>
        <div style="display: flex; gap: 8px; align-items: center;">
          <a-tooltip :title="getDisplayModeTooltip()">
            <a-button
              size="small"
              @click="toggleRelationDisplayMode"
              :icon="getDisplayModeIcon()"
              style="flex-shrink: 0;"
            >
              {{ getDisplayModeText() }}
            </a-button>
          </a-tooltip>
          <a-form-item labelAlign="left" >
            <a-input-search
              v-model:value="regGroupSearch.keyword"
              allow-clear
              size="small"
              placeholder="项目名称"
              @input="onRegGroupSearch"
              style="flex: 1;"
            />


          </a-form-item>
          <template v-if="hasPermission('reg:discount') && props.showPrice">
            <a-form-item labelAlign="left" tooltip="折扣率与折后总价只能设置一个，同时设置时以折扣率为准">
              <a-input-number
                v-model:value="batchFormState.disRate"
                size="small"
                placeholder="折扣率"
                :disabled="!hasPermission('reg:discount') || !allowModifyItems"
              />
            </a-form-item>
            <a-form-item labelAlign="left">
              <a-input-number
                v-model:value="batchFormState.priceAfterDis"
                size="small"
                placeholder="折后总价"
                :disabled="!hasPermission('reg:discount') || !allowModifyItems"
              />
            </a-form-item>
          </template>
          <a-form-item labelAlign="left">
            <a-select
              size="small"
              v-model:value="batchFormState.type"
              placeholder="项目类型"
              style="width: 95px"
              :disabled="!allowModifyItems"
              :options="[
              { label: '健康项目', value: '健康项目' },
              { label: '职业项目', value: '职业项目' },
            ]"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" size="small" html-type="submit" @click="updateByBatch" :disabled="!allowModifyItems">整体设置</a-button>
          </a-form-item>
        </div>
      </a-form>

      <a-table
        size="small"
        :style="tableStyle"
        :row-class-name="rowClassName"
        :class="['ant-table-striped']"
        :scroll="{ y: '80vh' }"
        :loading="regGroupLoading"
        :bordered="false"
        :pagination="false"
        row-key="id"
        :columns="regGroupColumn"
        :data-source="filteredRegGroupDataSource"
        :row-selection="{
          selectedRowKeys: regGroupTableState.selectedRowKeys,
          onChange: onRegGroupTableSelectChange,
          getCheckboxProps: (record) => ({
            disabled: record.payStatus == '已退款',
          }),
        }"
      >
        <template #bodyCell="{ column, text, record, index }">
          <template v-if="'itemGroupName' == column.dataIndex">
            <div style="display: flex; justify-content: space-between; align-items: center">
              <template v-if="record.addMinusFlag == 1">
                <a-tooltip :title="getItemTooltipText(text, record.checkPartName)" trigger="click">
                  <div style="display: flex; align-items: center; gap: 2px; overflow: hidden;">
                    <!-- 项目关系前缀显示 -->
                    <span
                      v-if="getItemRelationPrefix(record.itemGroupId)"
                      :style="{
                        color: getItemRelationPrefix(record.itemGroupId).color,
                        fontSize: '12px',
                        fontWeight: 500,
                        flexShrink: 0,
                        userSelect: 'none'
                      }"
                      :title="getItemRelationPrefix(record.itemGroupId).title"
                    >
                      {{ getItemRelationPrefix(record.itemGroupId).text }}
                    </span>

                    <!-- 加项标识和项目名称 -->
                    <span class="added-item" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit'">
                      加 {{ formatItemDisplayName(text, record.checkPartName) }}
                    </span>

                    <!-- 可选的badge显示 -->
                    <a-tag
                      v-if="getItemSourceBadge(record.itemGroupId) && showDetailedBadges"
                      :title="getItemSourceBadge(record.itemGroupId).title"
                      size="small"
                      :style="{
                        margin: 0,
                        fontSize: '10px',
                        lineHeight: '16px',
                        padding: '0 4px',
                        fontWeight: 500,
                        backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                        color: getItemSourceBadge(record.itemGroupId).color,
                        border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                        cursor: 'pointer'
                      }"
                      @click.stop.prevent="showItemRelationDetail(record.itemGroupId)"
                    >
                      {{ getItemSourceBadge(record.itemGroupId).text }}
                    </a-tag>
                  </div>
                </a-tooltip>
              </template>
              <template v-else-if="record.addMinusFlag == -1">
                <div style="display: flex; align-items: center; gap: 4px; overflow: hidden;">
                  <span class="removed-item" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap">减 {{ text }}</span>
                  <a-tag
                    v-if="getItemSourceBadge(record.itemGroupId)"
                    :title="getItemSourceBadge(record.itemGroupId).title"
                    size="small"
                    :style="{
                      margin: 0,
                      fontSize: '10px',
                      lineHeight: '16px',
                      padding: '0 4px',
                      fontWeight: 500,
                      backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                      color: getItemSourceBadge(record.itemGroupId).color,
                      border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                      cursor: 'pointer'
                    }"
                    @click="showItemRelationDetail(record.itemGroupId)"
                  >
                    {{ getItemSourceBadge(record.itemGroupId).text }}
                  </a-tag>
                </div>
              </template>
              <template v-else>
                <a-tooltip :title="getItemTooltipText(text, record.checkPartName)" trigger="click">
                  <div style="display: flex; align-items: center; gap: 2px; overflow: hidden;">
                    <!-- 项目关系前缀显示 -->
                    <span
                      v-if="getItemRelationPrefix(record.itemGroupId)"
                      :style="{
                        color: getItemRelationPrefix(record.itemGroupId).color,
                        fontSize: '12px',
                        fontWeight: 500,
                        flexShrink: 0,
                        userSelect: 'none'
                      }"
                      :title="getItemRelationPrefix(record.itemGroupId).title"
                    >
                      {{ getItemRelationPrefix(record.itemGroupId).text }}
                    </span>

                    <!-- 项目名称 -->
                    <span
                      :style="{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        color: getItemSourceType(record.itemGroupId) !== 'main' ? '#666' : 'inherit'
                      }"
                    >
                      {{ formatItemDisplayName(text, record.checkPartName) }}
                    </span>

                    <!-- 保留原有的badge（可选显示） -->
                    <a-tag
                      v-if="getItemSourceBadge(record.itemGroupId) && showDetailedBadges"
                      :title="getItemSourceBadge(record.itemGroupId).title"
                      size="small"
                      :style="{
                        margin: 0,
                        fontSize: '10px',
                        lineHeight: '16px',
                        padding: '0 4px',
                        fontWeight: 500,
                        backgroundColor: getItemSourceBadge(record.itemGroupId).bg,
                        color: getItemSourceBadge(record.itemGroupId).color,
                        border: `1px solid ${getItemSourceBadge(record.itemGroupId).bg}`,
                        cursor: 'pointer'
                      }"
                      @click="showItemRelationDetail(record.itemGroupId)"
                    >
                      {{ getItemSourceBadge(record.itemGroupId).text }}
                    </a-tag>
                  </div>
                </a-tooltip>
              </template>
            </div>
          </template>
          <template v-else-if="'seq' == column.dataIndex">
            {{ index + 1 }}
          </template>
          <template v-else-if="'disRate' == column.dataIndex">
            <input
              v-if="hasPermission('reg:discount') && props.showPrice"
              type="number"
              min="0"
              step="0.1"
              @change="handleDisRateChange(record, $event)"
              :value="record.disRate"
              :readonly="record.payStatus != '待支付' || !allowModifyItems"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
            />
            <span v-else>
              {{ record.disRate }}
            </span>
          </template>
          <template v-else-if="'priceAfterDis' == column.dataIndex">
            <input
              v-if="hasPermission('reg:discount') && props.showPrice"
              type="number"
              min="0"
              @change="handlePriceChange(record, $event)"
              :value="record.priceAfterDis"
              :readonly="record.payStatus != '待支付' || !allowModifyItems"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
            />
            <span v-else>
              {{ record.priceAfterDis }}
            </span>
          </template>
          <template v-else-if="'minDiscountRate' == column.dataIndex">
            <input
              v-if="hasPermission('reg:minDiscountRate') && props.showPrice"
              type="number"
              min="0"
              step="0.1"
              @change="updateMinDiscountRate(record, $event)"
              :value="record.minDiscountRate"
              :readonly="record.payStatus != '待支付' || !allowModifyItems"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
            />
            <span v-else>
              {{ record.minDiscountRate }}
            </span>
          </template>
          <template v-else-if="column.dataIndex == 'type'">
            <a-select
              @change="updateType(record)"
              size="small"
              :disabled="record.payStatus != '待支付' || !allowModifyItems"
              v-model:value="record.type"
              style="width: 90px"
              :options="[
                { label: '健康项目', value: '健康项目' },
                { label: '职业项目', value: '职业项目' },
              ]"
            />
          </template>
          <template v-else-if="column.dataIndex == 'payerType'">
            <a-select
              v-if="props.showPrice"
              @change="updatePayerType(record)"
              size="small"
              :disabled="record.payStatus != '待支付' || !customerReg.teamId || !allowModifyItems"
              v-model:value="record.payerType"
              style="width: 90px"
              :options="[
                { label: '个人支付', value: '个人支付' },
                { label: '单位支付', value: '单位支付' },
              ]"
            />
            <span v-else>
              {{ record.payerType }}
            </span>
          </template>
          <template v-else-if="column.dataIndex == 'payStatus'">
            <template v-if="record.payStatus == '退款中' || record.payStatus == '退款成功' || record.payStatus == '退款失败'">
              {{ record.payStatus }}
            </template>
            <template v-else-if="record.payStatus == '支付成功' || record.payStatus == '已支付'">
              {{ record.payStatus }}
            </template>
            <template v-else>
              {{ record.payStatus }}
            </template>
          </template>
          <template v-else-if="column.dataIndex == 'operation'">
            <template v-if="record.actionType === '退费' && hasPermission('fee:customer_reg_recipe:refund') && allowModifyItems">
              <a @click="doActionType(record)">{{ record.actionType }}</a>
            </template>
            <template v-else-if="allowModifyItems && record.actionType">
              <a-popconfirm :title="`确定${record.actionType}吗?`" ok-text="确定" cancel-text="取消" @confirm="doActionType(record)">
                <a>{{ record.actionType }}</a>
              </a-popconfirm>
            </template>
            <template v-else-if="record.actionType">
              <a-typography-text type="secondary">{{ record.actionType }}</a-typography-text>
            </template>
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </a-card>
    <!--    <fee-refund-modal ref="feeRefundModal" @success="handleRefundSuccess" />-->
    <bill-refund-modal ref="feeRefundModal" @success="handleRefundSuccess" />
    <customer-reg-group-pannel ref="regGroupPannel" @cancel="open" :show-price="props.showPrice" />
    <must-check-item-group-modal ref="mustCheckItemGroupModal" />
    <ColorSettingsModal :initialColors="initialTableStyle" @apply-colors="applyStripedStyles" ref="colorModalRef" />
    <RelatedLimitAmountOperationListModal
      ref="relatedLimitOperationRecordListModal"
      :selfLimitBalance="selfLimitBalance"
      :shareLimitBalance="shareLimitBalance"
    />
    <CustomerRegItemGroupStatusModal ref="statusModal" />
    <HistoryRegListModal ref="historyRegListModal" @success="handleHistoryItem" />
    <TeamLimitList4TransferAnotherModal ref="teamLimitList4TransferAnotherModal" @success="handleTransfer2Another" />
    <CardTransferModal ref="cardTransferModal" @success="handleTransfer2CardSuccess" />
    <a-modal title="额度操作" v-model:open="limitOperation.visiable" width="40%" @ok="doLimitOperation" @cancel="closeOperationModal">
      <a-form class="antd-modal-form" v-bind="limitOperation" ref="opreationFormRef" :labelCol="operationLabelCol" :wrapperCol="operationWrapperCol">
        <a-row>
          <a-col :span="24">
            <a-form-item label="操作类型">
              <a-typography-text type="danger" strong>{{ limitOperation.operationType }}</a-typography-text>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="金额">
              <a-input-number v-model:value="limitOperation.amount" />
            </a-form-item>
          </a-col>
          <a-col :span="24" v-if="limitOperation.operationType == '扣减'">
            <a-form-item label="限额扣减凭证">
              <j-image-upload :fileMax="0" v-model:value="limitOperation.limitPic" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
    <LimitOperationRecordModal ref="limitOperationRecordModal" />

    <!-- 部位选择模态框 -->
    <a-modal
      title="选择检查部位"
      v-model:open="checkPartState.visible"
      width="600px"
      @ok="confirmAddItemWithParts"
      @cancel="closeCheckPartSelector"
      :confirmLoading="checkPartState.loading"
      :okButtonProps="{ disabled: checkPartState.selectedParts.length === 0 }"
      okText="确认添加"
      cancelText="取消"
      @keydown="handleModalKeydown"
    >
      <div style="padding: 10px; height: 50vh; overflow-y: auto">
        <div style="margin-bottom: 8px"> <strong>项目：</strong>{{ checkPartState.currentItemGroup?.name }} </div>

        <a-form-item required>
          <a-select
            ref="checkPartSelectRef"
            v-model:value="checkPartState.selectedParts"
            mode="multiple"
            placeholder="请选择检查部位，支持多选，可输入关键字搜索（支持拼音缩写）"
            :filter-option="false"
            :loading="checkPartState.loading"
            :options="checkPartState.options"
            @search="searchCheckParts"
            @keydown="handleSelectKeydown"
            style="width: 100%"
            show-search
            allow-clear
            :not-found-content="checkPartState.loading ? '搜索中...' : '暂无数据'"
            :max-tag-count="3"
            :max-tag-text-length="8"
          >
            <template v-if="checkPartState.loading" #suffixIcon>
              <a-spin size="small" />
            </template>
          </a-select>
        </a-form-item>

        <!-- 已选择的部位显示 -->
        <div v-if="checkPartState.selectedParts.length > 0" style="margin-bottom: 16px">
          <div style="margin-bottom: 8px; font-weight: 500"> 已选择部位 ({{ checkPartState.selectedParts.length }})： </div>
          <div style="display: flex; flex-wrap: wrap; gap: 8px">
            <a-tag v-for="partId in checkPartState.selectedParts" :key="partId" closable @close="removeSelectedPart(partId)" color="blue">
              {{ getPartNameById(partId) }}
            </a-tag>
          </div>
        </div>

        <div style="color: #666; font-size: 12px; line-height: 1.5">
          <div>💡 使用提示：</div>
          <div>• 选择多个部位将为每个部位创建独立的检查项目记录</div>
          <div>• 支持按部位名称、拼音缩写搜索</div>
          <div>• 按 <kbd>Enter</kbd> 键快速保存，<kbd>Esc</kbd> 键取消</div>
        </div>
      </div>
    </a-modal>

    <!-- 套餐部位补充弹窗 -->
    <SuitPartSelectionModal ref="suitPartModalRef" @confirm="handleSuitPartConfirm" @cancel="handleSuitPartCancel" />

    <!-- 依赖项目快捷添加模态框 -->
    <DependencyQuickAddModal
      ref="dependencyQuickAddModalRef"
      @quick-add="handleDependencyQuickAdd"
      @confirm="handleDependencyConfirm"
      @cancel="handleDependencyCancel"
    />

    <!-- 项目关系详情模态框 -->
    <a-modal
      v-model:open="relationDetailModal.visible"
      title="项目关系详情"
      width="800px"
      :footer="null"
      @cancel="closeRelationDetailModal"
    >
      <div style="padding:10px">
        <div v-if="relationDetailModal.loading" style="text-align: center; padding: 40px;">
          <a-spin size="large" />
          <div style="margin-top: 16px;">正在加载关系详情...</div>
        </div>

        <div v-else-if="relationDetailModal.itemInfo && relationDetailModal.relationData" style="padding: 16px 0;">
          <!-- 当前项目信息 -->
          <div style="margin-bottom: 24px;">
            <h4 style="margin-bottom: 12px; color: #1890ff;">
              <span style="margin-right: 8px;">📋</span>当前项目信息
            </h4>
            <div style="background: #f5f5f5; padding: 12px; border-radius: 6px;">
              <div><strong>项目名称：</strong>{{ relationDetailModal.itemInfo.itemGroupName }}</div>
              <div v-if="relationDetailModal.itemInfo.checkPartName" style="margin-top: 4px;">
                <strong>检查部位：</strong>{{ relationDetailModal.itemInfo.checkPartName }}
              </div>
              <div style="margin-top: 4px;">
                <strong>项目类型：</strong>
                <a-tag :color="getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.bg" style="margin-left: 8px;color: #fff">
                  {{ getItemSourceBadge(relationDetailModal.itemInfo.itemGroupId)?.text }}
                </a-tag>
              </div>
            </div>
          </div>

          <!-- 关系来源信息 -->
          <div v-if="relationDetailModal.relationData.sourceInfo" style="margin-bottom: 24px;">
            <h4 style="margin-bottom: 12px; color: #52c41a;">
              <span style="margin-right: 8px;">🔗</span>关系来源
            </h4>
            <div style="background: #f6ffed; border: 1px solid #b7eb8f; padding: 12px; border-radius: 6px;">
              <div><strong>来源项目：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.itemGroupName }}</div>
              <div v-if="relationDetailModal.relationData.sourceInfo.mainItem.checkPartName" style="margin-top: 4px;">
                <strong>来源部位：</strong>{{ relationDetailModal.relationData.sourceInfo.mainItem.checkPartName }}
              </div>
              <div style="margin-top: 8px;">
                <strong>关系类型：</strong>
                <span v-if="relationDetailModal.relationData.sourceInfo.type === 'dependent'" style="color: #fa8c16;">
                依赖关系 - 当前项目是上述项目的依赖项目
              </span>
                <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'gift'" style="color: #52c41a;">
                赠送关系 - 当前项目是上述项目的赠送项目
              </span>
                <span v-else-if="relationDetailModal.relationData.sourceInfo.type === 'attach'" style="color: #722ed1;">
                附属关系 - 当前项目是上述项目的附属项目
              </span>
              </div>
            </div>
          </div>

          <!-- 说明信息 -->
          <div style="background: #e6f7ff; border: 1px solid #91d5ff; padding: 12px; border-radius: 6px;">
            <h4 style="margin-bottom: 8px; color: #1890ff;">
              <span style="margin-right: 8px;">💡</span>说明
            </h4>
            <div style="color: #666; line-height: 1.6;">
              <div v-if="relationDetailModal.relationData.sourceInfo?.type === 'dependent'">
                • 此项目是依赖项目，当添加来源项目时会自动添加<br/>
                • 依赖项目通常是检查必需的基础项目或前置项目<br/>
                • 删除来源项目时，此项目也会被自动删除
              </div>
              <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'gift'">
                • 此项目是赠送项目，当添加来源项目时会免费获得<br/>
                • 赠送项目通常是促销活动或套餐优惠的一部分<br/>
                • 删除来源项目时，此项目也会被自动删除
              </div>
              <div v-else-if="relationDetailModal.relationData.sourceInfo?.type === 'attach'">
                • 此项目是附属项目，与来源项目形成组合<br/>
                • 附属项目通常是主项目的补充检查或相关项目<br/>
                • 删除来源项目时，此项目也会被自动删除
              </div>
            </div>
          </div>
        </div>

        <div v-else style="text-align: center; padding: 40px; color: #999;">
          暂无关系详情信息
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
import { computed, h, inject, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';
import { CompanyTeam, CompanyTeamItemGroup, CustomerRegItemGroup, ICustomerReg, ItemGroup, Key, CheckPartDict } from '#/types';
import { getItemGroupByTeam, getTeamById } from '@/views/reg/CompanyReg.api';
import { getGroupOfSuit, listSuitByKeyword } from '@/views/basicinfo/ItemSuit.api';
import {
  addCustomerReg,
  addCustomerRegForSuit,
  getItemGroupByCustomerRegId,
  getItemGroupWithDependencyAnalysis,
  getRemainingAmount,
  handleAddItem,
  minusItemGroup,
  removeItemGroup,
  sendItemGroup2Interface,
  undoMinusItemGroup,
  updateItemGroup,
  addItemGroupWithCheckParts,
} from '@/views/reg/CustomerReg.api';
import { list as listItemGroup, listByRiskFactor, listItemGroupByKeyword } from '@/views/basicinfo/ItemGroup.api';
import { Empty, message, theme } from 'ant-design-vue';
import { selectedCustomerRegKey } from '@/providekey/provideKeys';
import { useMessage } from '@/hooks/web/useMessage';
import { usePermission } from '/@/hooks/web/usePermission';
import { BasicColumn } from '@/components/Table';
import { useListPage } from '@/hooks/system/useListPage';
import { debounce } from 'lodash-es';
import { isItemGroupAvailable, isSuitAvailable } from '@/utils/itemGroupValidation';
import CustomerRegGroupPannel from '@/views/reg/components/CustomerRegGroupPannel.vue';
import {
  DownOutlined,
  MenuOutlined,
  ProfileOutlined,
  BranchesOutlined,
  TagOutlined,
  AppstoreOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons-vue';
import { v4 as uuidv4 } from 'uuid';
import MustCheckItemGroupModal from '@/views/reg/components/MustCheckItemGroupModal.vue';
import ColorSettingsModal from '@/components/colorSetting/ColorSettingsModal.vue';
import BillRefundModal from '@/views/fee/components/BillRefundModal.vue';
import RelatedLimitAmountOperationListModal from '@/views/fee/RelatedLimitAmountOperationListModal.vue';
import CustomerRegItemGroupStatusModal from '@/views/summary/components/CustomerRegItemGroupStatusModal.vue';
import DependencyQuickAddModal from '@/components/DependencyQuickAddModal.vue';
import {
  checkItemMutex,
  formatConflictMessage,
  checkItemDependencies,
  checkAllItemsDependencies,
  formatDependencyMessage,
  getMissingDependencyDetails,
  preloadRelationData,
  analyzeItemSources,
  getItemRelations,
} from '@/utils/itemGroupRelationManager';
import { DependencyChecker } from '@/utils/DependencyChecker';

import HistoryRegListModal from '@/views/reg/components/HistoryRegListModal.vue';
import CardTransferModal from '@/views/fee/components/CardTransferModal.vue';
import LimitOperationRecordModal from '@/views/fee/LimitOperationRecordModal.vue';
import { subtractLimitAmount, transfer2Another } from '@/views/fee/LimitOperationRecord.api';
import JImageUpload from '@/components/Form/src/jeecg/components/JImageUpload.vue';
import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
import TeamLimitList4TransferAnotherModal from '@/views/reg/components/TeamLimitList4TransferAnotherModal.vue';
import { listByItemGroup } from '@/views/basicinfo/CheckPartDict.api';
import SuitPartSelectionModal from './components/SuitPartSelectionModal.vue';

const { token } = theme.useToken();
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const props = defineProps({
  customerReg: {
    type: [String, Object],
    default: '',
  },
  showPrice: {
    type: Boolean,
    default: true,
  },
});

const { hasPermission } = usePermission();
const { createConfirm, createErrorModal } = useMessage();
const emits = defineEmits(['cancel', 'addItemDone', 'refresh']);
const feeRefundModal = ref();
const title = computed(() => (selectedCustomerReg.value.name ? selectedCustomerReg.value.name + '的项目组合' : '项目组合'));
const customerRegId = computed<string>(() => selectedCustomerReg.value?.id);
const customerReg = computed<ICustomerReg>(() => selectedCustomerReg.value);
const companyTeam = ref<CompanyTeam>({});
// const spendAmount = ref<number>(0);
const remainingBalance = ref<number>(0);
const selfLimitBalance = ref<number>(0);
const shareLimitBalance = ref<number>(0);
const statusModal = ref<any>({});

// 判断是否允许修改项目列表
const allowModifyItems = computed<boolean>(() => {
  // 如果总检状态为未总检或者为空，则允许修改
  // return !customerReg.value?.summaryStatus || customerReg.value?.summaryStatus === '未总检';
  return customerReg.value?.summaryStatus !== '审核通过';
});

let originList: CustomerRegItemGroup[] = [];
let groupListFromAppoint: CustomerRegItemGroup[] = [];
/**余额操作*/
const limitOperation = reactive({
  visiable: false,
  operationType: '',
  amount: 0,
  limitPic: '',
});
const operationLabelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
const operationWrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
const teamLimitList4TransferAnotherModal = ref(null);
const limitOperationRecordModal = ref(null);
const cardTransferModal = ref(null);
const sendItemGroup2HisFlag = ref('0');

// 部位选择相关状态
const checkPartState = reactive({
  visible: false,
  loading: false,
  options: [] as Array<{ label: string; value: string; frequency: number }>,
  selectedParts: [] as string[],
  currentItemGroup: null as ItemGroup | null,
});

// 部位选择器引用
const checkPartSelectRef = ref(null);

// 套餐部位补充弹窗引用
const suitPartModalRef = ref(null);

// 依赖项目快捷添加模态框引用
const dependencyQuickAddModalRef = ref(null);

// 缺失的依赖项目列表
const missingDependencies = ref([]);
// 添加依赖项目的loading状态
const addingDependencies = ref(false);
// 依赖检查缓存时间戳
const lastDependencyCheckTime = ref(0);
// 依赖检查缓存时长（5分钟）
const DEPENDENCY_CHECK_CACHE_DURATION = 5 * 60 * 1000;

// 项目来源分析结果
const itemSourceMap = ref(new Map());
// 项目来源分析的loading状态
const analyzingItemSources = ref(false);

// 依赖关系详情模态框状态
const relationDetailModal = ref({
  visible: false,
  loading: false,
  itemInfo: null,
  relationData: null,
});

const limitDetailDesc = computed(() => {
  let desc = [];
  if (selfLimitBalance.value !== null) {
    desc.push(`本人￥${selfLimitBalance.value}`);
  }
  if (shareLimitBalance.value !== null) {
    desc.push(`亲友￥${shareLimitBalance.value}`);
  }
  return desc.join(',');
});
function openLimitOperationModal(operationType: string) {
  if (remainingBalance.value <= 0) {
    message.error('剩余额度不足，无法操作!');
    return;
  }
  limitOperation.visiable = true;
  limitOperation.operationType = operationType;
  limitOperation.amount = selfLimitBalance.value;
  limitOperation.limitPic = '';
}

function closeOperationModal() {
  limitOperation.visiable = false;
}

function doLimitOperation() {
  if (limitOperation.operationType == '扣减') {
    handelSubLimit();
  } else if (limitOperation.operationType == '转让') {
    openTeamLimit4Transfer();
    closeOperationModal();
  } else if (limitOperation.operationType == '转储') {
    openTransferModal();
    closeOperationModal();
  }
}

function openTeamLimit4Transfer() {
  teamLimitList4TransferAnotherModal.value?.open({ companyRegId: customerReg.value.companyRegId });
}
function handleTransfer2Another(datas) {
  if (!datas || datas.length == 0) {
    message.error('请选择转让对象');
    return;
  }
  if (remainingBalance.value <= 0) {
    message.error('剩余额度不足，无法转让');
    return;
  }

  let data = datas[0];
  let reqData = {
    regId: customerReg.value.id,
    targetLimitId: data.id,
    amount: limitOperation.amount,
  };
  transfer2Another(reqData).then((res) => {
    if (res.success) {
      message.success(res.message);
      emits('refresh');
    } else {
      message.error(res.message);
    }
  });
}

function handleTransfer2CardSuccess() {
  emits('refresh');
}

function openTransferModal() {
  cardTransferModal.value?.open({ regId: customerReg.value.id, amount: limitOperation.amount });
}

function handelSubLimit() {
  subtractLimitAmount({ regId: customerReg.value.id, amount: limitOperation.amount, limitPic: limitOperation.limitPic }).then((res) => {
    if (res.success) {
      message.success(res.message);
      emits('refresh');
      closeOperationModal();
    } else {
      message.error(res.message);
    }
  });
}

function showLimitOperationRecord() {
  limitOperationRecordModal.value?.open(customerReg.value);
}

/**历年体检弹窗*/
const historyRegListModal = ref();
const openHistoryRegListModal = () => {
  if (!customerReg.value) {
    message.error('请选择体检人!');
    return;
  }
  if (!allowModifyItems.value) {
    message.error('当前总检状态是审核通过状态，无法使用历史体检项目!');
    return;
  }
  historyRegListModal.value.open(customerReg.value);
};

const handleHistoryItem = (data: ItemGroup[]) => {
  console.log('handleHistoryItem:', data);
  if (!allowModifyItems.value) {
    message.error('当前总检状态是审核通过状态，无法添加历史体检项目!');
    return;
  }
  //将data批量加入
  handleAddBatch(data);
};

/**检查状态*/
const showCheckStatus = () => {
  statusModal.value.open(customerReg.value);
};

/**团检限额相关*/
const relatedFeePayRecordListModal = ref();
const relatedLimitOperationRecordListModal = ref();
const openRelatedLimitOperationRecordListModal = () => {
  relatedLimitOperationRecordListModal.value.open(customerReg.value);
};


/**弹窗*/
const regGroupPannel = ref();
function openGroupPannel() {
  regGroupPannel.value.open();
}

//组合项目列表
const regGroupColumn = computed(() => {
  const baseColumns = [
    {
      title: '',
      dataIndex: 'seq',
      width: '15px',
      align: 'center',
    },
    {
      title: '组合名称',
      dataIndex: 'itemGroupName',
      key: 'itemGroupName',
      ellipsis: true,
      width: '120px',
    },
  ];

  const priceColumns = props.showPrice
    ? [
      {
        title: '原价',
        dataIndex: 'price',
        width: '50px',
      },
      {
        title: '折后价',
        dataIndex: 'priceAfterDis',
        width: '40px',
      },
      {
        title: '折扣率',
        dataIndex: 'disRate',
        width: '40px',
      },
      {
        title: '最低折',
        dataIndex: 'minDiscountRate',
        width: '40px',
        ellipsis: true,
      },
    ]
    : [];

  const statusColumns = [
    {
      title: '状态',
      dataIndex: 'payStatus',
      width: '40px',
      ellipsis: true,
    }
  ];
  const operationColumns =[
    {
      title: '操作人',
      dataIndex: 'updateName',
      width: '30px',
      ellipsis: true,
    },
    {
      title: '操作时间',
      dataIndex: 'updateTime',
      width: '60px',
      ellipsis: true,
    },
  ];
  return [...baseColumns, ...priceColumns, ...statusColumns,...operationColumns];
});

const groupColumns = computed<BasicColumn[]>(() => {
  const baseColumns = [
    {
      title: '组合名称',
      dataIndex: 'name',
      ellipsis: true,
      width: 150,
    },
  ];

  const priceColumns = props.showPrice
    ? [
      {
        title: '价格',
        dataIndex: 'price',
        ellipsis: false,
        width: 80,
      },
    ]
    : [];

  const departmentColumns = [
    {
      title: '科室',
      dataIndex: 'departmentName',
      ellipsis: true,
      width: 80,
    },
  ];

  return [...baseColumns, ...priceColumns, ...departmentColumns];
});
const queryParam = reactive({ name: '', departmentId: '', helpChar: '' });
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    api: listItemGroup,
    rowKey: 'id',
    columns: groupColumns,
    canResize: false,
    clickToRowSelect: false,
    size: 'small',
    showTableSetting: false,
    actionColumn: {
      width: 60,
      fixed: 'right',
    },
    useSearchForm: false,
    beforeFetch: (params) => {
      return Object.assign(params, queryParam);
    },
    customRow: (record) => {
      return {
        class: 'no-select',
        onDblclick: () => {
          handleAddOne(record);
        },
      };
    },
  },
});

function handleAddItemDone() {
  handleAddItem({ id: customerRegId.value }).then((res) => {
    if (res.success) {
      message.success(res.message);
      // 重新加载数据
      open();
      emits('addItemDone');
    } else {
      message.error(res.message);
    }
  });
}

function handleSendGroup2His() {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要推送的项目');
    return;
  }
  let sengGroupList = regGroupTableState.selectedRows;

  let sendNames = sengGroupList.map((item, index) => index + 1 + '、' + item.itemGroupName);

  createConfirm({
    iconType: 'warning',
    title: '批量推送项目确认',
    content: sendNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let sendIds = sengGroupList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: sendIds,
      };
      sendItemGroup2Interface(info)
        .then((res) => {
          if (res.success) {
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功推送${sendIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('sengGroup2His', err);
        });
    },
  });
}

// Retrieve stored tableStyle from localStorage
const storedTableStyle = JSON.parse(localStorage.getItem('tableStyle') || '{}');
const initialTableStyle = {
  odd: storedTableStyle['--striped-odd-color'] || '#f9f9f9',
  even: storedTableStyle['--striped-even-color'] || '#ffffff',
  fontOdd: storedTableStyle['--font-color-odd'] || '#000000',
  fontEven: storedTableStyle['--font-color-even'] || '#000000',
};

// Define a reactive object to store dynamic styles
const tableStyle = reactive({
  '--striped-odd-color': storedTableStyle['--striped-odd-color'] || '#f9f9f9',
  '--striped-even-color': storedTableStyle['--striped-even-color'] || '#ffffff',
  '--font-color-odd': storedTableStyle['--font-color-odd'] || '#000000',
  '--font-color-even': storedTableStyle['--font-color-even'] || '#000000',
});
const colorModalRef = ref();
const showColorModal = () => {
  colorModalRef.value?.openModal();
};

// 定义方法应用条纹颜色和字体颜色
function applyStripedStyles(data) {
  console.log('applyStripedStyles:', data);
  tableStyle['--striped-odd-color'] = data.odd;
  tableStyle['--striped-even-color'] = data.even;
  tableStyle['--font-color-odd'] = data.fontOdd;
  tableStyle['--font-color-even'] = data.fontEven;
  localStorage.setItem('tableStyle', JSON.stringify(tableStyle));
}

// 修改 rowClassName 方法以区分奇数行和偶数行
function rowClassName(record, index) {
  return index % 2 === 0 ? 'table-striped odd-row' : 'table-striped even-row';
}

const [registerTable, { reload }, { rowSelection, selectedRows }] = tableContext;

const selectedCustomerReg = inject(selectedCustomerRegKey, {
  value: {},
  setValue: (val) => (selectedCustomerReg.value = val),
});

let lastFetchId = 0;

// 定义 reactive 的 state 对象
const quikSearchState = reactive({
  data: [], // 下拉选项的数据
  value: [], // 选中的值
  fetching: false, // 加载状态
});

// 部位搜索状态
const partSearchState = reactive({
  selectedPart: null, // 选中的部位ID（单选）
  options: [], // 部位选项
  loading: false, // 部位加载状态
  adding: false, // 添加操作加载状态
  currentProject: null, // 当前项目数据
  dropdownOpen: false, // 控制下拉列表是否打开
});

// 引用
const quickSearchSelectRef = ref(null);
const partSelectRef = ref(null);

// 定义搜索方法
const fetchItemGroup = debounce((value: string) => {
  lastFetchId += 1;
  const fetchId = lastFetchId;
  quikSearchState.data = [];
  quikSearchState.fetching = true;

  if (value.startsWith('--')) {
    // 搜索套餐
    const keyword = value.substring(2).trim(); // 去掉开头的 '--'
    listSuitByKeyword({ keyword: keyword, pageSize: 50 })
      .then((res) => {
        if (fetchId !== lastFetchId) {
          return;
        }
        if (res.success) {
          quikSearchState.data = res.result?.map((item) => ({
            label: `[套餐] ${item.name}`,
            value: item.id,
            isPackage: true,
            itemData: item, // 存储完整的套餐数据
          }));
        }
        quikSearchState.fetching = false;
      })
      .catch((error) => {
        console.error('Fetch item suit error:', error);
        quikSearchState.data = [];
        quikSearchState.fetching = false;
      });
  } else {
    // 搜索项目组合
    listItemGroupByKeyword({ keyword: value, pageSize: 50 })
      .then((res) => {
        if (fetchId !== lastFetchId) {
          return;
        }
        if (res.success) {
          quikSearchState.data = res.result.map((item) => ({
            label: `${item.name} - ${item.price}元`,
            value: item.id,
            isPackage: false,
            itemData: item, // 存储完整的项目数据
          }));
        }

        quikSearchState.fetching = false;
      })
      .catch((error) => {
        console.error('Fetch item group error:', error);
        quikSearchState.data = [];
        quikSearchState.fetching = false;
      });
  }
}, 300);

async function handleSearchChange(options) {
  const selectedOption = options.option;
  if (selectedOption?.itemData) {
    if (selectedOption.isPackage) {
      // 选择的是套餐，添加套餐内的所有项目
      const suit = selectedOption.itemData;
      setGroupBySuit(suit, true); // 跳过确认对话框

      // 重置状态
      await nextTick();
      quikSearchState.value = [];
      quikSearchState.data = [];
      quikSearchState.fetching = false;
    } else {
      // 选择的是项目
      const itemGroup = selectedOption.itemData;

      if (itemGroup.hasCheckPart == '1') {
        // 需要选择部位的项目，加载部位选项并聚焦到部位选择框
        partSearchState.currentProject = itemGroup;
        await loadPartsForCurrentProject(itemGroup.id);

        // 聚焦到部位选择框并自动打开下拉列表
        await nextTick();
        if (partSelectRef.value) {
          partSelectRef.value.focus();
          // 自动打开下拉列表
          setTimeout(() => {
            partSearchState.dropdownOpen = true;
          }, 100);
        }
      } else {
        // 不需要部位的项目，直接添加
        handleAddOne(itemGroup);

        // 重置状态
        await nextTick();
        quikSearchState.value = [];
        quikSearchState.data = [];
        quikSearchState.fetching = false;
        resetPartSearchState();
      }
    }
  }
}

// 部位搜索相关方法
async function loadPartsForCurrentProject(projectId: string) {
  partSearchState.loading = true;
  partSearchState.selectedPart = null;

  try {
    console.log('Loading parts for project:', projectId);
    const params = { itemGroupId: projectId };
    const res = await listByItemGroup(params);

    console.log('Parts API response:', res);

    let parts = [];
    if (Array.isArray(res)) {
      parts = res;
    } else if (res && Array.isArray(res.result)) {
      parts = res.result;
    } else if (res && res.success && Array.isArray(res.result)) {
      parts = res.result;
    }

    if (parts.length > 0) {
      partSearchState.options = parts.map((item: any) => {
        const frequency = item.frequency || 0;

        return {
          label: item.name,
          value: item.id,
          frequency: frequency,
          partData: item,
        };
      });

      // 按使用频次排序
      partSearchState.options.sort((a, b) => {
        if (b.frequency !== a.frequency) {
          return b.frequency - a.frequency;
        }
        return a.label.localeCompare(b.label);
      });
    } else {
      partSearchState.options = [];
      console.warn('No parts found for project:', projectId);
    }
  } catch (error) {
    console.error('Load parts error:', error);
    partSearchState.options = [];
    message.error('加载部位选项失败: ' + error.message);
  } finally {
    partSearchState.loading = false;
  }
}

function filterPartOption(input: string, option: any) {
  // 空输入时显示所有选项
  if (!input || !input.trim()) {
    return true;
  }

  const searchText = input.toLowerCase().trim();
  const label = option.label || '';
  const partData = option.partData || {};
  let helpChar = partData.helpChar || '';
  const code = partData.code || '';
  const name = partData.name || partData.partName || '';


  // 多字段模糊匹配：标签、名称、拼音缩写、编码
  const matchLabel = label.toLowerCase().includes(searchText);
  const matchName = name.toLowerCase().includes(searchText);
  const matchHelpChar = helpChar.toLowerCase().includes(searchText);
  const matchCode = code.toLowerCase().includes(searchText);

  const isMatch = matchLabel || matchName || matchHelpChar || matchCode;

  return isMatch;
}

function handlePartSelectFocus() {
  console.log('Part select focused, auto opening dropdown');
  // 聚焦时自动打开下拉列表
  if (partSearchState.options.length > 0) {
    partSearchState.dropdownOpen = true;
  }
}

// 处理部位选择变化事件（点击选中时触发）
function handlePartSelectChange(value) {
  console.log('Part selected via click:', value);
  if (value && partSearchState.currentProject && !partSearchState.adding) {
    // 延迟一下执行，确保选择状态已更新
    setTimeout(() => {
      handleQuickAdd();
    }, 100);
  }
}

function resetPartSearchState() {
  partSearchState.selectedPart = null;
  partSearchState.options = [];
  partSearchState.currentProject = null;
  partSearchState.adding = false;
  partSearchState.dropdownOpen = false;
}

// 键盘事件处理
function handleQuickSearchKeydown(event: KeyboardEvent) {
  // 如果是 Enter
  if (event.key === 'Enter' && !event.shiftKey && !event.ctrlKey && !event.altKey) {
    console.log('Enter pressed in quick search, current project:', partSearchState.currentProject);

    if (partSearchState.currentProject) {
      if (partSearchState.currentProject.hasCheckPart == '1') {
        // 需要部位的项目，聚焦到部位选择框
        console.log('Project needs parts, focusing to part select');
        event.preventDefault();

        setTimeout(() => {
          if (partSelectRef.value) {
            partSelectRef.value.focus();
            // 自动打开下拉列表
            setTimeout(() => {
              partSearchState.dropdownOpen = true;
            }, 100);
          }
        }, 50);
      } else {
        // 不需要部位的项目，直接添加
        console.log('Project does not need parts, adding directly');
        event.preventDefault();

        setTimeout(() => {
          handleQuickAdd();
        }, 50);
      }
    } else {
      console.log('No current project selected');
    }
  }
}

// 移除handlePartSelectKeydown函数，避免与@change事件重复触发
// 现在只通过@change事件处理部位选择，避免回车键和选择变化的重复触发

async function handleQuickAdd() {
  if (!partSearchState.currentProject) {
    message.warning('请先选择项目');
    return;
  }

  const projectData = partSearchState.currentProject;
  // 在方法开始时保存选中的部位，避免在执行过程中被清空
  const selectedPart = partSearchState.selectedPart;

  // 添加调试信息
  console.log('handleQuickAdd called:', {
    projectData,
    hasCheckPart: projectData.hasCheckPart,
    selectedPart: selectedPart,
    customerRegId: customerRegId.value,
    selectedCustomerRegId: selectedCustomerReg.value?.id,
  });

  partSearchState.adding = true;

  try {
    if (projectData.hasCheckPart == '1') {
      if (!selectedPart) {
        message.warning('该项目需要选择检查部位');
        partSearchState.adding = false;
        return;
      }

      // 有部位的项目，构造itemGroups数组
      console.log('Adding project with parts:', projectData.id, selectedPart);

      // 获取部位信息
      const partInfoMap = {};
      partSearchState.options.forEach((option) => {
        partInfoMap[option.value] = option.partData;
      });

      const partInfo = partInfoMap[selectedPart];
      const partName = partInfo?.name;

      // 检查是否重复
      const isDuplicate = checkItemPartCombinationDuplicate(regGroupDataSource.value, projectData.id, selectedPart, true);
      if (isDuplicate) {
        message.warn(`${projectData.name} - ${partName} 已存在`);
        partSearchState.adding = false;
        return;
      }

      // 使用现有的generateCustomerRegItemGroup方法生成基础数据
      const baseData = generateCustomerRegItemGroup(projectData);
      if (!baseData) {
        partSearchState.adding = false;
        return;
      }

      // 设置部位相关信息
      baseData.checkPartId = selectedPart;
      baseData.checkPartName = partName;
      baseData.checkPartCode = partInfo?.code || '';

      const itemGroups = [baseData];

      // 调用API添加项目
      const params = {
        customerRegId: customerRegId.value,
        itemGroups: itemGroups,
      };

      console.log('API call params:', params);
      const res = await addItemGroupWithCheckParts(params);

      if (res.success) {
        message.success(`成功添加项目"${projectData.name} - ${partName}"`);
      } else {
        message.error(res.message || '添加失败，请重试');
        partSearchState.adding = false;
        return;
      }
    } else {
      // 无部位的项目，直接添加
      console.log('Adding project without parts:', projectData);

      // 检查是否正在添加项目，防止重复
      if (regGroupLoading.value) {
        console.log('正在添加项目，请勿重复操作');
        partSearchState.adding = false;
        return;
      }

      // 调用handleAddOne，它会处理loading状态
      handleAddOne(projectData);

      // 等待添加完成（通过监听loading状态）
      const waitForAddComplete = () => {
        return new Promise((resolve) => {
          const checkLoading = () => {
            if (!regGroupLoading.value) {
              resolve();
            } else {
              setTimeout(checkLoading, 100);
            }
          };
          checkLoading();
        });
      };

      await waitForAddComplete();

      // 无部位项目添加完成后，清空项目选择并聚焦到搜索框
      quikSearchState.value = [];
      quikSearchState.data = [];
      quikSearchState.fetching = false;
      resetPartSearchState();

      // 重新聚焦到快捷搜索框
      await nextTick();
      quickSearchSelectRef.value?.focus();
      return;
    }

    // 有部位项目添加完成后，只重置部位选择状态，保持项目选择不变
    partSearchState.selectedPart = null;

    // 刷新项目列表
    lastDependencyCheckTime.value = 0;
    await fetchCustomerRegGroupList(selectedCustomerReg.value.id);

    // 重新聚焦到部位选择框，方便继续添加其他部位
    await nextTick();
    partSelectRef.value?.focus();
  } catch (error) {
    console.error('Quick add error:', error);
    message.error('添加项目失败: ' + error.message);
  } finally {
    partSearchState.adding = false;
  }
}

/**职业病体检必检项目*/
const mustCheckItemGroupModal = ref(null);
const mustCheckItemGroup = ref<ItemGroup[]>([]);
//过滤出dataSource中没有的必检项目 - 考虑部位信息
const mustCheckItemGroupFiltered = computed(() => {
  return mustCheckItemGroup.value.filter((item) => {
    // 使用统一的判重逻辑（计算属性中禁用警告提示，避免死循环）
    return !checkItemGroupDuplicate(regGroupDataSource.value, item, false);
  });
});
const mustCheckItemTip = computed(() => {
  return `必检项目:${mustCheckItemGroup.value.length}个，${mustCheckItemGroupFiltered.value.length}个待添加！`;
});
function fetchMustCheckItemGorup() {
  listByRiskFactor({ riskFactorIds: customerReg.value.riskFactor, post: customerReg.value.jobStatus }).then((res) => {
    if (res.success) {
      mustCheckItemGroup.value = res.result;
      //根据dataSource中是否包含必检项目，修改mustCheckAdded状态 - 考虑部位信息
      console.log('==========================', regGroupDataSource.value);
      mustCheckItemGroup.value.forEach((item) => {
        item.mustCheckAdded = checkItemGroupDuplicate(regGroupDataSource.value, item, false);
      });
    }
  });
}
function openMustCheckItemModal() {
  mustCheckItemGroupModal.value?.open(mustCheckItemGroup.value);
}

function addMustCheckItem() {
  const itemsToAdd = mustCheckItemGroupFiltered.value.map(item => ({
    ...item,
    type: '职业项目'
  }));
  // 必检项目添加也是自动添加，禁用警告提示
  handleAddBatch(itemsToAdd, true);
}

/**右侧登记中关联的项目组合相关操作*/
const regGroupSearch = reactive({ keyword: '' });
const regGroupLoading = ref<boolean>(false);
const regGroupDataSource = ref<CustomerRegItemGroup[]>([]);
const filteredRegGroupDataSource = computed(() => {
  // 先按关键字过滤
  const keyword = regGroupSearch.keyword.trim().toLowerCase();
  let filteredData = regGroupDataSource.value;

  if (keyword) {
    filteredData = filteredData.filter((item) => {
      const name = item.itemGroupName ? item.itemGroupName.toLowerCase() : '';
      const pinyin = item.helpChar ? item.helpChar.toLowerCase() : '';
      if (name.includes(keyword) || pinyin.includes(keyword)) {
        return true;
      }

      return false;
    });
  }

  // 构建主项目和子项目的层级结构
  return buildHierarchicalStructure(filteredData);
});

// 构建层级结构：主项目 + 其子项目（基于后端返回的依赖关系数据）
function buildHierarchicalStructure(data) {
  console.log('=== buildHierarchicalStructure 开始 ===');
  console.log('输入数据数量:', data.length);

  const result = [];
  const processedItems = new Set();

  // 按照加项和减项进行大分类
  const categorizedData = {
    added: data.filter(item => item.addMinusFlag === 1),
    normal: data.filter(item => item.addMinusFlag !== 1 && item.addMinusFlag !== -1),
    reduced: data.filter(item => item.addMinusFlag === -1)
  };

  console.log('分类结果:', {
    added: categorizedData.added.length,
    normal: categorizedData.normal.length,
    reduced: categorizedData.reduced.length
  });

  // 新的处理逻辑：基于后端返回的依赖关系数据建立父子关系
  // 1. 建立父子关系映射
  const parentChildMap = buildParentChildMap(data);
  console.log('父子关系映射:', parentChildMap);

  // 2. 找出所有主项目（不分类别）
  const allMainItems = data.filter(item => {
    const sourceType = getItemSourceType(item.itemGroupId);
    return sourceType === 'main';
  });

  console.log('找到的所有主项目:', allMainItems.map(item => `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`));

  // 2. 按照加减项标志对主项目进行排序
  const sortedMainItems = allMainItems.sort((a, b) => {
    // 加项(1) -> 正常(0) -> 减项(-1)
    const getOrder = (flag) => {
      if (flag === 1) return 0;  // 加项最前
      if (flag === -1) return 2; // 减项最后
      return 1; // 正常项目在中间
    };
    return getOrder(a.addMinusFlag) - getOrder(b.addMinusFlag);
  });

  console.log('排序后的主项目:', sortedMainItems.map(item => `${item.itemGroupName} (${item.id}) - 加减项标志: ${item.addMinusFlag}`));

  // 3. 为每个主项目添加其子项目
  sortedMainItems.forEach(mainItem => {
    if (processedItems.has(mainItem.id)) {
      console.log(`跳过已处理的主项目: ${mainItem.itemGroupName} (${mainItem.id})`);
      return;
    }

    console.log(`--- 处理主项目: ${mainItem.itemGroupName} (${mainItem.id}) ---`);

    // 添加主项目
    result.push(mainItem);
    processedItems.add(mainItem.id);
    console.log(`添加主项目: ${mainItem.itemGroupName} (${mainItem.id})`);

    // 查找并添加该主项目的所有子项目
    const childItems = findChildItemsFast(mainItem, data, parentChildMap);
    console.log(`找到 ${childItems.length} 个子项目:`, childItems.map(child => `${child.itemGroupName} (${child.id})`));

    childItems.forEach(childItem => {
      if (!processedItems.has(childItem.id)) {
        console.log(`添加子项目: ${childItem.itemGroupName} (${childItem.id})`);
        result.push(childItem);
        processedItems.add(childItem.id);
      } else {
        console.log(`跳过已处理的子项目: ${childItem.itemGroupName} (${childItem.id})`);
      }
    });
  });

  // 4. 处理没有找到主项目的孤立子项目（按分类排序）
  ['added', 'normal', 'reduced'].forEach(category => {
    const categoryData = categorizedData[category];

    categoryData.forEach(item => {
      if (!processedItems.has(item.id)) {
        const sourceType = getItemSourceType(item.itemGroupId);
        if (sourceType !== 'main') {
          console.log(`添加孤立子项目: ${item.itemGroupName} (${item.id}) - 类型: ${sourceType}`);
          result.push(item);
          processedItems.add(item.id);
        }
      }
    });
  });

  console.log('=== buildHierarchicalStructure 结束 ===');
  console.log('最终结果数量:', result.length);
  console.log('最终结果:', result.map((item, index) => `${index + 1}. ${item.itemGroupName} (${item.id}) - 加减项: ${item.addMinusFlag}`));

  return result;
}

// 基于后端返回的依赖关系数据建立父子关系映射
function buildParentChildMap(data) {
  const parentChildMap = new Map(); // parentId -> [childItem1, childItem2, ...]

  console.log('🔍 开始建立父子关系映射...');

  data.forEach(item => {
    const sourceType = getItemSourceType(item.itemGroupId);

    if (sourceType !== 'main') {
      // 这是一个子项目，需要找到它的父项目
      const parentItem = findParentItem(item, data);
      if (parentItem) {
        const parentId = parentItem.id;
        if (!parentChildMap.has(parentId)) {
          parentChildMap.set(parentId, []);
        }
        parentChildMap.get(parentId).push(item);
        console.log(`   ✅ 找到父子关系: ${parentItem.itemGroupName} -> ${item.itemGroupName} (${sourceType})`);
      } else {
        console.log(`   ⚠️ 未找到父项目: ${item.itemGroupName} (${sourceType})`);
      }
    }
  });

  console.log(`🎯 父子关系映射完成，共找到 ${parentChildMap.size} 个父项目`);
  return parentChildMap;
}

// 根据后端返回的依赖关系数据找到子项目的父项目
function findParentItem(childItem, allItems) {
  const childSourceType = getItemSourceType(childItem.itemGroupId);

  // 遍历所有项目，找到包含此子项目的父项目
  for (const potentialParent of allItems) {
    if (potentialParent.id === childItem.id) continue;

    const parentSourceType = getItemSourceType(potentialParent.itemGroupId);
    if (parentSourceType !== 'main') continue;

    // 检查父项目的依赖关系数据
    if (isChildOfParent(childItem, potentialParent, childSourceType)) {
      return potentialParent;
    }
  }

  return null;
}

// 检查子项目是否属于父项目
function isChildOfParent(childItem, parentItem, childSourceType) {
  console.log(`🔍 检查父子关系: ${parentItem.itemGroupName} -> ${childItem.itemGroupName} (${childSourceType})`);

  // 基于后端返回的依赖关系数据进行判断
  switch (childSourceType) {
    case 'dependent':
      if (parentItem.dependentGroups && Array.isArray(parentItem.dependentGroups)) {
        const isDependent = parentItem.dependentGroups.some(dep => dep.relationGroupId === childItem.itemGroupId);
        console.log(`   依赖关系检查: ${isDependent}, 父项目依赖列表:`, parentItem.dependentGroups.map(d => d.relationGroupId));
        return isDependent;
      }
      break;

    case 'attach':
      if (parentItem.attachGroups && Array.isArray(parentItem.attachGroups)) {
        const isAttach = parentItem.attachGroups.some(attach => attach.relationGroupId === childItem.itemGroupId);
        console.log(`   附属关系检查: ${isAttach}, 父项目附属列表:`, parentItem.attachGroups.map(a => a.relationGroupId));
        return isAttach;
      }
      break;

    case 'gift':
      if (parentItem.giftGroups && Array.isArray(parentItem.giftGroups)) {
        const isGift = parentItem.giftGroups.some(gift => gift.relationGroupId === childItem.itemGroupId);
        console.log(`   赠送关系检查: ${isGift}, 父项目赠送列表:`, parentItem.giftGroups.map(g => g.relationGroupId));
        return isGift;
      }
      break;
  }

  // 降级到原有的字段判断逻辑
  console.log(`   使用降级逻辑检查父子关系`);
  const isChildByLegacy = childItem.attachBaseId === parentItem.id ||
         childItem.parentGroupId === parentItem.itemGroupId ||
         (childItem.itemSuitId && parentItem.itemSuitId && childItem.itemSuitId === parentItem.itemSuitId);

  console.log(`   降级逻辑结果: ${isChildByLegacy}`);
  console.log(`   - attachBaseId: ${childItem.attachBaseId} === ${parentItem.id} ? ${childItem.attachBaseId === parentItem.id}`);
  console.log(`   - parentGroupId: ${childItem.parentGroupId} === ${parentItem.itemGroupId} ? ${childItem.parentGroupId === parentItem.itemGroupId}`);
  console.log(`   - 套餐关系: ${childItem.itemSuitId} === ${parentItem.itemSuitId} ? ${childItem.itemSuitId && parentItem.itemSuitId && childItem.itemSuitId === parentItem.itemSuitId}`);

  return isChildByLegacy;
}

// 快速查找主项目的子项目（基于父子关系映射）
function findChildItemsFast(mainItem, dataList, parentChildMap) {
  console.log(`--- findChildItemsFast for ${mainItem.itemGroupName} (${mainItem.id}) ---`);

  // 优先使用父子关系映射
  if (parentChildMap && parentChildMap.has(mainItem.id)) {
    const childItems = parentChildMap.get(mainItem.id);
    console.log(`✅ 从父子关系映射中找到 ${childItems.length} 个子项目:`, childItems.map(child => child.itemGroupName));
    return sortChildItems(childItems);
  }

  // 降级到原有逻辑
  console.log('⚠️ 父子关系映射中未找到，使用原有逻辑查找...');
  const childItems = [];
  const mainCreateTime = mainItem.createTime ? new Date(mainItem.createTime).getTime() : 0;

  dataList.forEach(item => {
    if (item.id === mainItem.id) return;

    const sourceType = getItemSourceType(item.itemGroupId);

    // 如果是非主项目，检查是否可能是该主项目的子项目
    if (sourceType !== 'main') {
      let isChild = false;
      let reason = '';

      // 1. 检查attachBaseId关系（附属项目）
      if (item.attachBaseId === mainItem.id) {
        isChild = true;
        reason = 'attachBaseId匹配';
      }

      // 2. 检查attachGroupIds关系（主项目的附属项目列表）
      if (!isChild && mainItem.attachGroupIds && Array.isArray(mainItem.attachGroupIds)) {
        if (mainItem.attachGroupIds.includes(item.id)) {
          isChild = true;
          reason = 'attachGroupIds包含';
        }
      }

      // 3. 检查parentGroupId关系
      if (!isChild && item.parentGroupId === mainItem.itemGroupId) {
        isChild = true;
        reason = 'parentGroupId匹配';
      }

      // 4. 检查套餐关系
      if (!isChild && item.itemSuitId && mainItem.itemSuitId &&
        item.itemSuitId === mainItem.itemSuitId) {
        // 同一套餐的项目，如果一个是主项目，另一个是非主项目，建立关系
        isChild = true;
        reason = '套餐关系';
      }

      console.log(`检查项目 ${item.itemGroupName} (${item.id}): ${isChild ? '是子项目' : '不是子项目'} ${reason ? '- ' + reason : ''}`);

      if (isChild) {
        childItems.push(item);
      }
    }
  });

  console.log(`找到 ${childItems.length} 个子项目:`, childItems.map(child => child.itemGroupName));

  return sortChildItems(childItems);
}

// 对子项目进行排序
function sortChildItems(childItems) {
  // 按关系类型排序：依赖 -> 赠送 -> 附属 -> 套餐
  const sortedChildren = childItems.sort((a, b) => {
    const aType = getItemSourceType(a.itemGroupId);
    const bType = getItemSourceType(b.itemGroupId);

    const typeOrder = { 'dependent': 1, 'gift': 2, 'attach': 3, 'suit': 4 };
    return (typeOrder[aType] || 999) - (typeOrder[bType] || 999);
  });

  console.log(`排序后的子项目:`, sortedChildren.map(child => `${child.itemGroupName}(${getItemSourceType(child.itemGroupId)})`));

  return sortedChildren;
}

// 调试函数：打印项目关系识别结果
function debugProjectRelations() {
  console.log('=== 项目关系识别调试信息 ===');
  regGroupDataSource.value.forEach(item => {
    const sourceType = getItemSourceType(item.itemGroupId);
    console.log(`项目: ${item.itemGroupName}`);
    console.log(`  - 记录ID: ${item.id}`);
    console.log(`  - 项目组ID: ${item.itemGroupId}`);
    console.log(`  - 加减项标志: ${item.addMinusFlag}`);
    console.log(`  - 识别类型: ${sourceType}`);
    console.log(`  - 套餐ID: ${item.itemSuitId || '无'}`);
    console.log(`  - 赠检标志: ${item.giveAwayFlag || '无'}`);
    console.log(`  - 父项目ID: ${item.parentGroupId || '无'}`);
    console.log(`  - 附属基础ID: ${item.attachBaseId || '无'}`);
    console.log(`  - 附属项目IDs: ${item.attachGroupIds ? JSON.stringify(item.attachGroupIds) : '无'}`);
    console.log(`  - 创建时间: ${item.createTime || '无'}`);
    console.log('---');
  });
  console.log('=== 调试信息结束 ===');
}

// 简化的项目关系检查（基于项目自身数据，不使用时间推测）
function isRelatedItem(item1, item2) {
  // 检查是否为相关项目（同一套餐、明确的关系字段）
  if (item1.itemSuitId && item2.itemSuitId && item1.itemSuitId === item2.itemSuitId) {
    return true;
  }

  if (item1.parentGroupId === item2.itemGroupId || item2.parentGroupId === item1.itemGroupId) {
    return true;
  }

  // 检查attachBaseId关系
  if (item1.attachBaseId === item2.id || item2.attachBaseId === item1.id) {
    return true;
  }

  // 检查attachGroupIds关系
  if (item1.attachGroupIds && Array.isArray(item1.attachGroupIds) && item1.attachGroupIds.includes(item2.id)) {
    return true;
  }

  if (item2.attachGroupIds && Array.isArray(item2.attachGroupIds) && item2.attachGroupIds.includes(item1.id)) {
    return true;
  }

  return false;
}
const onRegGroupSearch = debounce(() => {
  // The computed property automatically updates
}, 300);

const regGroupTableState = reactive<{
  selectedRowKeys: Key[];
  selectedRows: CustomerRegItemGroup[];
  loading: boolean;
}>({
  selectedRowKeys: [],
  selectedRows: [],
  loading: false,
});

const onRegGroupTableSelectChange = async (selectedRowKeys: Key[], selectedRows: CustomerRegItemGroup[]) => {
  let newSelectedRowKeys = [...selectedRowKeys];
  let newSelectedRows = [...selectedRows];
  // 判断是否为全选操作
  const isSelectAll = selectedRowKeys.length === filteredRegGroupDataSource.value.length;
  if (!isSelectAll) {
    // 遍历选中的行
    for (const row of selectedRows) {
      if (row.attachGroupIds && row.attachGroupIds.length > 0) {
        const confirmResult = await new Promise<boolean>((resolve) => {
          createConfirm({
            iconType: 'warning',
            title: '确认是否勾选附属项目！',
            content: '选中项目有附属项目，是否勾选附属项目？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => resolve(true),
            onCancel: () => resolve(false),
          });
        });

        if (confirmResult) {
          // 勾选附属项目
          row.attachGroupIds.forEach((attachId) => {
            const attachRow = regGroupDataSource.value.find((item) => item.id === attachId);
            if (attachRow && !newSelectedRowKeys.includes(attachId)) {
              newSelectedRowKeys.push(attachId);
              newSelectedRows.push(attachRow);
            }
          });
        }
      }
    }
  }
  regGroupTableState.selectedRowKeys = newSelectedRowKeys;
  regGroupTableState.selectedRows = newSelectedRows;
};

const totalPrice = computed(() => {
  let amount = regGroupDataSource.value.reduce(
    (total, row) => (row.addMinusFlag != -1 && row.payStatus != '退款成功' ? total + row.price : total),
    0
  );
  return amount.toFixed(2);
});

const totalPriceAfterDis = computed(() => {
  let amount = regGroupDataSource.value.reduce(
    (total, row) => (row.addMinusFlag != -1 && row.payStatus != '退款成功' ? total + row.priceAfterDis : total),
    0
  );
  return amount.toFixed(2);
});


// 防止重复调用的标志
let fetchingInProgress = false;

// 完全使用后端统一依赖关系分析
async function fetchCustomerRegGroupList(id) {
  // 防止重复调用
  if (fetchingInProgress) {
    console.log('⚠️ fetchCustomerRegGroupList 正在执行中，跳过重复调用');
    return;
  }

  fetchingInProgress = true;
  regGroupLoading.value = true;

  try {
    // 使用后端统一依赖关系分析接口
    console.log('🚀 调用后端统一依赖分析接口，登记ID:', id);
    const response = await getItemGroupWithDependencyAnalysis({ regId: id });
    const analysisResult = response.result || response;

    if (analysisResult && analysisResult.items) {
      // 使用后端返回的完整数据
      regGroupDataSource.value = analysisResult.items;

      // 设置依赖关系摘要
      const summary = analysisResult.summary || {};
      missingDependencies.value = summary.missingDependencies || [];

      // 设置项目来源分析结果
      const sourceMap = new Map();
      regGroupDataSource.value.forEach(item => {
        if (item.sourceType) {
          sourceMap.set(item.itemGroupId, item.sourceType);
        }
      });
      itemSourceMap.value = sourceMap;

      console.log('✅ 后端依赖关系分析完成');
      console.log('   - 项目数量:', regGroupDataSource.value.length);
      console.log('   - 缺失依赖项目数量:', missingDependencies.value.length);
      console.log('   - 项目来源分析完成，类型数量:', sourceMap.size);
      console.log('   - API调用次数: 1次 (优化前需要', regGroupDataSource.value.length + 2, '次)');

      // 调试：打印项目来源类型信息
      console.log('📊 项目来源类型详情:');
      regGroupDataSource.value.forEach((item, index) => {
        console.log(`   ${index + 1}. ${item.itemGroupName} (${item.id}) - sourceType: ${item.sourceType || '未设置'}`);
      });
    } else {
      console.error('❌ 后端接口返回数据格式异常:', analysisResult);
      throw new Error('后端接口返回数据格式异常');
    }
  } catch (error) {
    console.error('❌ 获取项目依赖关系分析失败:', error);
    message.error('获取项目列表失败: ' + (error.message || '未知错误'));

    // 清空数据，避免显示错误信息
    regGroupDataSource.value = [];
    missingDependencies.value = [];
    itemSourceMap.value = new Map();

    regGroupLoading.value = false;
    fetchingInProgress = false; // 重置防重复调用标志
    return; // 出错时直接返回，不执行后续逻辑
  }

  // 处理团体和余额相关数据（保持原有逻辑）
  await handleTeamAndBalanceData();

  // 初始化组合的操作类型
  regGroupDataSource.value.forEach((item) => {
    item.actionType = getActionType(item);
  });

  regGroupLoading.value = false;
  fetchingInProgress = false; // 重置防重复调用标志
}

// 注意：已移除旧的降级逻辑，完全使用后端统一依赖分析



// 处理团体和余额相关数据的辅助函数
async function handleTeamAndBalanceData() {
  let teamId = selectedCustomerReg.value?.teamId;
  let originCustomerLimitAmountId = selectedCustomerReg.value?.originCustomerLimitAmountId;

  let promises = [];
  let promiseTypes = [];

  // 只有有teamId才调用getItemGroupByTeam和getTeamById接口
  if (teamId && teamId !== '0' && teamId !== 0 && teamId !== 'null' && teamId !== 'undefined') {
    promises.push(getItemGroupByTeam({ teamId: teamId }));
    promises.push(getTeamById({ id: teamId }));
    promiseTypes.push('teamGroups', 'teamInfo');
  } else {
    companyTeam.value = {};
    groupListFromAppoint = [];
  }

  // 有teamId或originCustomerLimitAmountId时调用getRemainingAmount
  if (teamId || originCustomerLimitAmountId) {
    promises.push(
      getRemainingAmount({
        customerId: selectedCustomerReg.value.customerId,
        teamId: teamId,
        originCustomerLimitAmountId: selectedCustomerReg.value.originCustomerLimitAmountId,
      })
    );
    promiseTypes.push('balance');
  }

  if (promises.length > 0) {
    try {
      const results = await Promise.all(promises);
      let resultIndex = 0;

      // 处理团体相关数据
      if (promiseTypes.includes('teamGroups')) {
        groupListFromAppoint = results[resultIndex].map((item: CompanyTeamItemGroup) => {
          return {
            itemGroupId: item.itemGroupId,
          };
        });
        companyTeam.value = results[resultIndex + 1];
        resultIndex += 2;
      }

      // 处理余额信息
      if (promiseTypes.includes('balance')) {
        const balanceInfo = results[resultIndex];
        remainingBalance.value = balanceInfo.totalLimitAmount;
        selfLimitBalance.value = balanceInfo.selfLimitAmount;
        shareLimitBalance.value = balanceInfo.shareLimitAmount;
      }
    } catch (error) {
      console.error('获取团体和余额数据失败:', error);
    }
  }

  // 初始化originRegGroupList
  if (teamId && groupListFromAppoint.length > 0) {
    originList = groupListFromAppoint;
  } else {
    let regStatus = selectedCustomerReg.value?.status;
    if (regStatus == '已登记') {
      originList = unref(regGroupDataSource.value);
    } else {
      originList = [];
    }
  }

  // 处理风险因素相关逻辑
  if (selectedCustomerReg.value.riskFactor) {
    fetchMustCheckItemGorup();
  }
}

function getRemoveType(itemGroup: CustomerRegItemGroup): string {
  let regStatus = selectedCustomerReg.value?.status;
  if (regStatus == '已登记') {
    if (itemGroup.addMinusFlag == -1) {
      return '反减';
    } else {
      return '减项';
    }
  } else {
    if (selectedCustomerReg.value?.teamId && groupListFromAppoint.length > 0) {
      if (itemGroup.addMinusFlag == -1) {
        return '反减';
      } else {
        return '减项';
      }
    } else {
      if (itemGroup.addMinusFlag == 1) {
        return '减项';
      } else if (itemGroup.addMinusFlag == -1) {
        return '反减';
      } else {
        return '删除';
      }
    }
  }
}

function getActionType(customerRegItemGroup: CustomerRegItemGroup): string {
  if (customerRegItemGroup.lockByRefund == '1') {
    return '';
  }
  if (customerRegItemGroup.payStatus == '待支付') {
    return getRemoveType(customerRegItemGroup);
  } else if (customerRegItemGroup.payStatus == '已支付') {
    if (customerRegItemGroup.firstCheckAfterPayFlag == '1') {
      return '删除';
    } else {
      return '退费';
    }
  }
  return '';
}

function getPayerType4Add(addMinusFlag: number): string {
  if (companyTeam.value?.payerType) {
    addMinusFlag = addMinusFlag != null ? addMinusFlag : getAddMinusFlag4Add();
    if (addMinusFlag == 1) {
      return companyTeam.value.addItemPayerType;
    } else {
      return companyTeam.value.payerType;
    }
  } else {
    return '个人支付';
  }
}

function getAddMinusFlag4Add(): number {
  if (selectedCustomerReg.value?.teamId) {
    if (groupListFromAppoint.length == 0) {
      if (selectedCustomerReg.value?.status == '已登记') {
        return 1;
      } else {
        return 0;
      }
    } else {
      return 1;
    }
  } else {
    if (selectedCustomerReg.value?.status == '已登记') {
      return 1;
    } else {
      return 0;
    }
  }
}

function updateRegGroupBatch(needDisGroup): Promise<void> {
  return new Promise((resolve, reject) => {
    let regGroupList = needDisGroup.map((item) => {
      return {
        id: item.id,
        customerRegId: unref(customerRegId),
        itemGroupName: item.itemGroupName,
        itemGroupId: item.itemGroupId,
        type: item.type,
        price: item.price,
        disRate: item.disRate,
        priceAfterDis: item.priceAfterDis,
        payerType: item.payerType,
        addMinusFlag: item.addMinusFlag,
        minDiscountRate: item.minDiscountRate,
        priceDisDiffAmount: item.priceDisDiffAmount,
      };
    });

    regGroupLoading.value = true;
    updateItemGroup({ data: regGroupList })
      .then((res) => {
        if (res.success) {
          message.success(res.message);
          resolve();
        } else {
          reject(new Error(res.message));
        }
      })
      .catch((error) => {
        reject(error);
      })
      .finally(() => {
        regGroupLoading.value = false;
      });
  });
}

function updateRegGroupByOne(record) {
  let data = [
    {
      id: record.id,
      customerRegId: unref(customerRegId),
      itemGroupName: record.itemGroupName,
      itemGroupId: record.itemGroupId,
      type: record.type,
      price: record.price,
      disRate: record.disRate,
      priceAfterDis: record.priceAfterDis,
      payerType: record.payerType,
      addMinusFlag: record.addMinusFlag,
      minDiscountRate: record.minDiscountRate,
      priceDisDiffAmount: record.priceDisDiffAmount,
    },
  ];
  regGroupLoading.value = true;
  updateItemGroup({ data: data })
    .then((res) => {
      if (res.success) {
        message.success(res.message);
      }
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
}

function doActionType(record: CustomerRegItemGroup) {
  if (record.actionType == '删除') {
    removeRegGroupByOne(record);
  } else if (record.actionType == '减项') {
    minusRegGroupByOne(record);
  } else if (record.actionType == '反减') {
    undoMinusRegGroupByOne(record);
  } else if (record.actionType == '退费') {
    //refundFee([record]);
    handleRefund([record]);
  }
}

const removeRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  removeItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        // 删除操作后强制重新检查依赖关系
        // 清除依赖检查缓存，确保重新检查
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('删除成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('removeItemGroup', err);
    });
};

const removeRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要删除的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let removeList = regGroupTableState.selectedRows.filter((item) => item.actionType == '删除');
  //console.log('removeList', removeList);
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');
  if (removeList.length == 0) {
    message.warn('没有可删除的项目');
    return;
  }
  //弹出确认框，在弹窗内展示删除的项目
  let removeNames = removeList.map((item, index) => index + 1 + '、' + item.itemGroupName);
  let tipContent = `<div style="max-height: 200px; overflow-y: scroll">${removeNames.join('<br/>')}</div>`;
  createConfirm({
    iconType: 'warning',
    title: '批量删除确认',
    content: tipContent,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let removeIds = removeList.map((item) => item.id);
      //let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: removeIds,
      };
      removeItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            // 批量删除操作后强制重新检查依赖关系
            // 清除依赖检查缓存，确保重新检查
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功删除${removeIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('removeItemGroup', err);
        });
    },
  });
};

const minusRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  minusItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        // 减项操作后强制重新检查依赖关系
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('minusRegGroupByOne', err);
    });
};

const minusRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要减项的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let minusList = regGroupTableState.selectedRows.filter((item) => item.actionType == '减项');
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');

  //console.log('minusList', minusList);
  if (minusList.length == 0) {
    message.warn('没有可减项的项目');
    return;
  }

  let minusNames = minusList.map((item, index) => index + 1 + '、' + item.itemGroupName);

  createConfirm({
    iconType: 'warning',
    title: '批量减项确认',
    content: minusNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: minusIds,
      };
      minusItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            // 批量减项操作后强制重新检查依赖关系
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功减项${minusIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('minusRegGroupBatch', err);
        });
    },
  });
};

const undoMinusRegGroupByOne = (record: CustomerRegItemGroup) => {
  let info = {
    regId: unref(customerRegId),
    ids: [record.id],
  };
  undoMinusItemGroup(info)
    .then((res) => {
      //console.log('removeItemGroup', res);
      if (res.success) {
        // 反减操作后强制重新检查依赖关系
        lastDependencyCheckTime.value = 0;
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('操作成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((err) => {
      console.log('minusRegGroupByOne', err);
    });
};

function refundFeeBatch() {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要退费的项目');
    return;
  }
  let refundList = regGroupTableState.selectedRows.filter((item) => item.actionType == '退费');
  if (refundList.length == 0) {
    message.warn('没有可退费的项目！');
    return;
  }
  handleRefund(refundList);
}

function handleRefund(records: CustomerRegItemGroup[]) {
  feeRefundModal.value.fetchData(records, customerReg.value);
}

function handleRefundSuccess() {
  fetchCustomerRegGroupList(selectedCustomerReg.value.id);
  regGroupTableState.selectedRows = [];
  regGroupTableState.selectedRowKeys = [];
}

const undoMinusRegGroupBatch = () => {
  if (regGroupTableState.selectedRowKeys.length == 0) {
    message.warn('请选择要反减的项目');
    return;
  }
  //筛选出removeType为移除的项目，和removeType为减项的项目
  let minusList = regGroupTableState.selectedRows.filter((item) => item.actionType == '反减');
  //let minusList = regGroupDataSource.value.filter((item) => item.actionType == '减项');

  //console.log('minusList', minusList);
  if (minusList.length == 0) {
    message.warn('没有可反减的项目');
    return;
  }

  let minusNames = minusList.map((item, index) => index + 1 + '、' + item.itemGroupName);

  createConfirm({
    iconType: 'warning',
    title: '批量反减确认',
    content: minusNames.join('<br/>'),
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      let minusIds = minusList.map((item) => item.id);
      let info = {
        regId: unref(customerRegId),
        ids: minusIds,
      };
      undoMinusItemGroup(info)
        .then((res) => {
          //console.log('removeItemGroup', res);
          if (res.success) {
            // 批量反减操作后强制重新检查依赖关系
            lastDependencyCheckTime.value = 0;
            fetchCustomerRegGroupList(selectedCustomerReg.value.id);
            regGroupTableState.selectedRows = [];
            regGroupTableState.selectedRowKeys = [];
            message.success(`成功反减${minusIds.length}个`);
          } else {
            message.error(res.message);
          }
        })
        .catch((err) => {
          console.log('minusRegGroupBatch', err);
        });
    },
  });
};

function handlePriceChange(record, event) {
  if (event.target.value == record.priceAfterDis) {
    return;
  }

  let priceAfterDis = Number(event.target.value);
  //判断折后价是否正确
  let rate = record.price != 0 ? priceAfterDis / record.price : 0;
  rate = parseFloat(rate.toFixed(2));
  let minDiscountRate = parseFloat(record.minDiscountRate) || 0;
  if (rate < minDiscountRate) {
    let minPrice = parseFloat((record.price * minDiscountRate).toFixed(2));
    createErrorModal({ title: '操作失败', content: '折后价过低，该项目可设置的最低折后价为' + minPrice + '元！' });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }

  record.priceAfterDis = priceAfterDis;
  record.disRate = rate;
  updateRegGroupByOne(record);
}

function handleDisRateChange(record, event) {
  if (event.target.value == record.disRate) {
    return;
  }
  let rate = Number(event.target.value);
  let minDiscountRate = record.minDiscountRate || 0;
  if (rate < minDiscountRate) {
    createErrorModal({ title: '操作失败', content: '折扣率不能小于最低折扣率' + minDiscountRate });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  record.disRate = parseFloat(rate.toFixed(2));
  record.priceAfterDis = parseFloat((record.price * rate).toFixed(2));
  updateRegGroupByOne(record);
}

function updateType(record) {
  if (record.type == null) {
    message.error('请选择类型');
    return;
  }
  updateRegGroupByOne(record);
}

function updateMinDiscountRate(record, event) {
  if (event.target.value == record.minDiscountRate) {
    return;
  }
  if (record.minDiscountRate < 0) {
    createErrorModal({ title: '操作失败', content: '最低折扣率不能小于0' });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  let rate = Number(event.target.value);
  let disRate = record.disRate || 0;
  if (disRate < rate) {
    createErrorModal({ title: '操作失败', content: '最低折扣率不能大于折扣率' + disRate });
    fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    return;
  }
  record.minDiscountRate = parseFloat(rate.toFixed(2));
  updateRegGroupByOne(record);
}

function updatePayerType(record) {
  if (record.payerType == null) {
    message.error('请选择支付方');
    return;
  }
  updateRegGroupByOne(record);
}

/**中间按钮相关*/
const removeBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '删除'));
const minusBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '减项'));
const undoMinusBtnEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '反减'));
const refundEnable = computed<Boolean>(() => regGroupTableState.selectedRows.some((item) => item.actionType == '退费'));

/**左侧体检项目组合相关操作*/
  // 修改 setGroupBySuit 方法
const setGroupBySuit = (suit, skipConfirm = false) => {
    const proceed = () => {
      //校验套餐是否适合当前登记,通过性别和年龄进行校验
      if (!isSuitAvailable(suit, customerReg.value)) {
        message.warn('套餐不适用当前体检人！');
        return;
      }

      groupLoading.value = true;
      getGroupOfSuit({ suitId: suit.id })
        .then((res) => {
          let addList = [];
          let unsuitableItems = [];
          let needPartSelectionItems = [];
          let groupList = unref(regGroupDataSource);

          res.forEach((group) => {
            // 调试：打印套餐项目的完整信息
            console.log('套餐项目数据:', {
              name: group.name,
              hasCheckPart: group.hasCheckPart,
              checkPartId: group.checkPartId,
              checkPartName: group.checkPartName,
              checkPartCode: group.checkPartCode,
            });

            // 检查项目是否需要部位选择
            if (group.hasCheckPart == '1') {
              // 如果套餐中已经预设了部位信息，则可以直接添加
              if (group.checkPartId && group.checkPartName) {
                console.log(`✅ 套餐中的项目 ${group.name} 使用预设部位：${group.checkPartName} (ID: ${group.checkPartId})`);
                // 继续处理，不跳过
              } else {
                console.log(`⚠️ 套餐中的项目 ${group.name} 需要部位选择但未预设部位，跳过自动添加`);
                needPartSelectionItems.push(group);
                return;
              }
            } else {
              console.log(`ℹ️ 套餐中的项目 ${group.name} 不需要部位选择`);
            }

            // 检查项目是否已存在 - 考虑部位信息（套餐添加时不显示警告）
            const isDuplicate = checkItemGroupDuplicate(groupList, group, false);
            if (isDuplicate) {
              console.log(`项目 ${group.name} 已存在，跳过添加`);
              return;
            }

            const { isValid, errorMessage } = isItemGroupAvailable(group, customerReg.value);
            if (!isValid) {
              unsuitableItems.push({ group, errorMessage });
            } else {
              group.priceAfterDis = group.priceAfterDisOfSuit;
              group.minDiscountRate = group.minDiscountRateOfSuit;
              group.disRate = group.disRateOfSuit;
              group.priceDisDiffAmount = group.priceDisDiffAmountOfSuit;
              let data = generateCustomerRegItemGroup(group);
              console.log('=======getGroupOfSuit================', group);
              if (data != null) {
                data.itemSuitName = suit.name;
                data.itemSuitId = suit.id;

                // 设置部位信息（如果套餐中有预设）
                if (group.checkPartId && group.checkPartName) {
                  data.checkPartId = group.checkPartId;
                  data.checkPartName = group.checkPartName;
                  data.checkPartCode = group.checkPartCode || '';
                  console.log(`✅ 设置部位信息成功：${group.name} - ${group.checkPartName} (ID: ${group.checkPartId}, Code: ${group.checkPartCode})`);
                  console.log('最终的data对象:', {
                    itemGroupName: data.itemGroupName,
                    checkPartId: data.checkPartId,
                    checkPartName: data.checkPartName,
                    checkPartCode: data.checkPartCode,
                  });
                } else {
                  console.log(`⚠️ 未设置部位信息：${group.name} - checkPartId: ${group.checkPartId}, checkPartName: ${group.checkPartName}`);
                }

                addList.push(data);
              }
            }
          });

          // 处理不适用的项目
          if (unsuitableItems.length > 0) {
            const unsuitableNames = unsuitableItems.map((item) => item.group.name).join(', ');
            const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
            createConfirm({
              iconType: 'warning',
              title: '不适用项目确认',
              content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
                h('p', `以下项目不适用：${unsuitableNames}。`),
                h('p', { innerHTML: errorMessages }),
                h('p', '是否继续添加适用的项目？'),
              ]),
              okText: '确认',
              cancelText: '取消',
              onOk: () => {
                proceedToAddItems(addList);
              },
              onCancel: () => {
                groupLoading.value = false;
              },
            });
          } else {
            if (addList.length == 0 && needPartSelectionItems.length == 0) {
              message.warn('套餐内项目已均已存在，无法使用该套餐！');
              groupLoading.value = false;
              return;
            }

            // 先添加不需要部位选择的项目
            if (addList.length > 0) {
              proceedToAddItems(addList, true); // 标记为套餐添加
            } else {
              groupLoading.value = false;
            }

            // 处理需要部位选择的项目
            if (needPartSelectionItems.length > 0) {
              console.log('Opening suit part selection modal for items:', needPartSelectionItems);
              console.log('suitPartModalRef.value:', suitPartModalRef.value);

              // 保存当前套餐信息，用于部位补充时设置套餐字段
              currentSuitInfo.value = {
                id: suit.id,
                name: suit.name,
              };

              if (suitPartModalRef.value) {
                console.log('Calling suitPartModalRef.value.open()');
                suitPartModalRef.value.open(needPartSelectionItems);
                console.log('Modal open method called');
              } else {
                console.error('suitPartModalRef.value is null or undefined');
              }
            }
          }
        })
        .finally(() => {
          // groupLoading.value = false; // 移到 proceedToAddItems 方法中
        });
    };

    if (skipConfirm) {
      // 跳过确认对话框，直接执行
      proceed();
    } else {
      // 显示确认对话框
      createConfirm({
        iconType: 'warning',
        title: '使用套餐确认',
        content: `套餐内项目将与现有项目合并，确定使用 ${suit.name} 吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: proceed,
      });
    }
  };

const groupLoading = ref<boolean>(false);

// Helper function to proceed with adding items
function proceedToAddItems(addList, isFromSuit = false) {
  if (addList.length == 0) {
    groupLoading.value = false;
    return;
  }

  // 如果是套餐添加，直接保存项目，不处理赠送和附属项目
  if (isFromSuit) {
    // 套餐添加：调用专用API，跳过赠送和附属项目逻辑
    addCustomerRegForSuit(addList)
      .then((res) => {
        if (res.success) {
          fetchCustomerRegGroupList(selectedCustomerReg.value.id);
          selectedRows.value = [];
          message.success(`成功添加套餐项目 ${addList.length} 条！（套餐中已包含赠送和附属项目）`);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        regGroupLoading.value = false;
        groupLoading.value = false;
      });
  } else {
    // 普通添加：使用原有逻辑，会自动处理赠送和附属项目
    addCustomerReg(addList)
      .then((res) => {
        if (res.success) {
          fetchCustomerRegGroupList(selectedCustomerReg.value.id);
          selectedRows.value = [];
          message.success(`成功添加 ${addList.length} 条！`);
        } else {
          message.error(res.message);
        }
      })
      .finally(() => {
        regGroupLoading.value = false;
        groupLoading.value = false;
      });
  }
}

// 通过套餐设置组合

/** 对登记中的体检项目进行整体设置*/
interface BatchState {
  disRate: number | null;
  priceAfterDis: number | null;
  type: string;
}

const batchFormState = reactive<BatchState>({
  disRate: null,
  priceAfterDis: null,
  type: '健康项目',
});

function updateByBatch() {
  //实际参与打折的项目
  const needDisGroup = regGroupDataSource.value.filter((row) => row.addMinusFlag !== -1 && row.payStatus === '待支付');
  if (!needDisGroup || needDisGroup.length == 0) {
    message.warn('没有可以设置的项目！');
    return;
  }
  if (
    (batchFormState.disRate === null || batchFormState.disRate === undefined) &&
    (batchFormState.priceAfterDis === null || batchFormState.priceAfterDis === undefined)
  ) {
    message.warn('请设置批量调整条件！');
    return;
  }

  /*  if (customerReg.value.paymentState !== '待支付') {
      message.warn('当前的支付状态下，无法进行整体设置！');
      return;
    }*/

  if (batchFormState.disRate !== null) {
    // 更新每一行的折扣率和折后价格
    const inputRate = Number(batchFormState.disRate);
    needDisGroup.forEach((row) => {
      // 确保折扣率不低于最小折扣率
      const rate = inputRate > row.minDiscountRate ? inputRate : row.minDiscountRate;
      row.disRate = rate;
      row.priceAfterDis = parseFloat((row.price * rate).toFixed(2));
    });
  } else if (batchFormState.priceAfterDis !== null) {
    //目标价格
    const targetTotalPrice = Number(batchFormState.priceAfterDis);
    //总原价
    const originalTotalPrice = needDisGroup.reduce((total, row) => total + row.price, 0);
    //总差价
    const totalDiffAmount = targetTotalPrice - originalTotalPrice;

    if (totalDiffAmount < 0) {
      //可折扣到的最低价格
      const minAvailableTotalPrice = needDisGroup.reduce((total, row) => {
        const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
        return total + minPrice;
      }, 0);
      //可以折扣的最多差价
      const totalAvailableDecrease = originalTotalPrice - minAvailableTotalPrice;

      if (-totalDiffAmount > totalAvailableDecrease) {
        createErrorModal({
          title: '操作失败',
          content: '设定的折后总价小于可设置的最小总价 ' + minAvailableTotalPrice.toFixed(2) + ' 元，无法执行该操作！',
        });
        return;
      }

      // 分配减少的金额
      needDisGroup.forEach((row) => {
        const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
        const availableDecrease = row.price - minPrice;
        const decreaseAmount = (availableDecrease / totalAvailableDecrease) * -totalDiffAmount;
        row.priceAfterDis = parseFloat((row.price - decreaseAmount).toFixed(2));
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    } else if (totalDiffAmount > 0) {
      // 需要增加价格
      // 暂不考虑价格上限
      needDisGroup.forEach((row) => {
        const increaseAmount = (row.price / originalTotalPrice) * totalDiffAmount;
        row.priceAfterDis = parseFloat((row.price + increaseAmount).toFixed(2));
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    } else {
      needDisGroup.forEach((row) => {
        row.priceAfterDis = row.price;
        row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
      });
    }

    // 调整因舍入造成的差异
    let finalTotalAmount = needDisGroup.reduce((total, row) => total + row.priceAfterDis, 0);
    let finalDiff = parseFloat((targetTotalPrice - finalTotalAmount).toFixed(2));

    if (finalDiff !== 0) {
      needDisGroup[0].priceAfterDis = parseFloat((needDisGroup[0].priceAfterDis + finalDiff).toFixed(2));
      needDisGroup[0].disRate = needDisGroup[0].price !== 0 ? parseFloat((needDisGroup[0].priceAfterDis / needDisGroup[0].price).toFixed(2)) : 0;
    }
  }

  // 更新类型
  needDisGroup.forEach((row) => {
    row.type = batchFormState.type;
  });
  updateRegGroupBatch(needDisGroup);
}

function generateCustomerRegItemGroup(row) {
  let addMinusFlag = getAddMinusFlag4Add();
  let payerType = getPayerType4Add(addMinusFlag);

  // 移除这里的loading设置，由调用方统一管理
  // regGroupLoading.value = true;
  let data: CustomerRegItemGroup = {
    uuid: uuidv4(),
    customerRegId: unref(customerRegId),
    examNo: customerReg.value.examNo,
    itemGroupName: row.name,
    itemGroupId: row.id,
    hisCode: row.hisCode,
    hisName: row.hisName,
    platCode: row.platCode,
    platName: row.platName,
    classCode: row.classCode,
    type: row.type ?? '健康项目',
    disRate: row.disRate != undefined && row.disRate != null ? row.disRate : 1,
    price: row.price,
    priceAfterDis: row.priceAfterDis != undefined && row.priceAfterDis != null ? row.priceAfterDis : row.price,
    payerType: payerType,
    departmentId: row.departmentId,
    departmentName: row.departmentName,
    departmentCode: row.departmentCode,
    addMinusFlag: addMinusFlag,
    payStatus: '待支付',
    minDiscountRate: row.minDiscountRate,
    priceDisDiffAmount: row.priceDisDiffAmount || 0,
    // 部位信息（如果有的话）
    checkPartId: row.checkPartId || null,
    checkPartName: row.checkPartName || null,
    checkPartCode: row.checkPartCode || null,
    checkStatus: '未检',
  };
  data.actionType = getActionType(data);

  return data;
}
// 添加选中的行添加到登记项目列表中
const handleAddOne = (row) => {
  // 防止重复点击
  if (regGroupLoading.value) {
    console.log('正在添加项目，请勿重复点击');
    return;
  }

  const { isValid, errorMessage } = isItemGroupAvailable(row, customerReg.value);
  if (!isValid) {
    message.warn(errorMessage);
    return;
  }

  // 检查是否需要部位选择
  if (row.hasCheckPart === '1') {
    showCheckPartSelector(row);
    return;
  }

  // 使用统一的判重逻辑
  const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, row);
  if (isDuplicate) {
    message.warn(`${row.name}已经存在`);
    return;
  }

  // 设置loading状态，防止重复点击
  regGroupLoading.value = true;

  let data = generateCustomerRegItemGroup(row);
  if (data == null) {
    message.warn(`${row.name}添加失败`);
    regGroupLoading.value = false;
    return;
  }
  if (companyTeam.value.limitAmount && remainingBalance.value <= 0) {
    data.payerType = '个人支付';
  }

  console.log('开始添加项目:', row.name, '请求数据:', data);

  addCustomerReg([data])
    .then((res) => {
      if (res.success) {
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
        message.success('添加成功');
      } else {
        message.error(res.message);
      }
    })
    .catch((error) => {
      console.error('添加项目失败:', error);
      message.error('添加项目失败，请重试');
    })
    .finally(() => {
      regGroupLoading.value = false;
    });
};

const handleAddBatch = (rows, isAutoAdd = false) => {
  if (rows.length == 0) {
    message.warn('请选择要添加的项目');
    return;
  }
  let addList = [];
  let unsuitableItems = [];
  let groupList = unref(regGroupDataSource);
  rows.forEach((row) => {
    // 检查项目是否已存在 - 考虑部位信息
    // 如果是自动添加（如依赖项目快捷添加），则禁用警告提示
    const isDuplicate = checkItemGroupDuplicate(groupList, row, !isAutoAdd);
    if (isDuplicate) {
      console.log(`项目 ${row.name} 已存在，跳过添加`);
      return;
    }

    const { isValid, errorMessage } = isItemGroupAvailable(row, customerReg.value);
    if (!isValid) {
      unsuitableItems.push({ row, errorMessage });
    } else {
      let data = generateCustomerRegItemGroup(row);
      if (data != null) {
        addList.push(data);
      }
    }
  });

  // 处理不适用的项目
  if (unsuitableItems.length > 0) {
    const unsuitableNames = unsuitableItems.map((item) => item.row.name).join(', ');
    const errorMessages = unsuitableItems.map((item) => item.errorMessage).join('<br/>');
    createConfirm({
      iconType: 'warning',
      title: '不适用项目确认',
      content: h('div', { style: 'max-height: 50vh; overflow-y: auto' }, [
        h('p', `以下项目不适用：${unsuitableNames}。`),
        h('p', { innerHTML: errorMessages }),
        h('p', '是否继续添加适用的项目？'),
      ]),
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        proceedToAddItems(addList);
      },
    });
  } else {
    if (addList.length == 0) {
      message.warn('已存在的项目已均已存在，无法使用该套餐！');
      return;
    }
    proceedToAddItems(addList);
  }
};

async function open() {
  regGroupDataSource.value = [];
  originList = [];
  selectedRows.value = [];
  regGroupTableState.selectedRowKeys = [];
  regGroupTableState.selectedRows = [];
  // 清空依赖项目提示
  missingDependencies.value = [];
  // 清空项目来源分析结果
  itemSourceMap.value = new Map();
  // 清空依赖检查缓存，确保重新检查
  lastDependencyCheckTime.value = 0;

  if (selectedCustomerReg.value && selectedCustomerReg.value.id) {
    await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
    console.log('✅ 项目列表加载完成，依赖关系已由后端统一分析');
  }
}
function handleSendItemGroup2HisFlag() {
  querySysParamByCode({ code: 'send_item_group_flag' }).then((res) => {
    sendItemGroup2HisFlag.value = res.result;
  });
}

//watch(regGroupDataSource, fetchSpendAmount, { deep: true });
watch(
  () => [selectedCustomerReg.value?.id, selectedCustomerReg.value?.payStatus],
  async () => {
    await open();
  }
);

// 部位选择相关方法
async function showCheckPartSelector(itemGroup: ItemGroup) {
  console.log('Showing check part selector for item group:', itemGroup);

  // 重置状态
  checkPartState.currentItemGroup = itemGroup;
  checkPartState.selectedParts = [];
  checkPartState.options = [];
  checkPartState.loading = false;

  // 显示模态框
  checkPartState.visible = true;

  // 加载部位选项
  await loadCheckParts(itemGroup.id);

  // 自动聚焦到选择器
  nextTick(() => {
    checkPartSelectRef.value?.focus();
  });
}

async function loadCheckParts(itemGroupId: string, keyword?: string) {
  checkPartState.loading = true;
  try {
    console.log('Loading check parts for itemGroupId:', itemGroupId, 'keyword:', keyword);
    const params = { itemGroupId };
    if (keyword && keyword.trim()) {
      params.keyword = keyword.trim();
    }

    console.log('API request params:', params);
    const res = await listByItemGroup(params);
    console.log('API response:', res);
    // 检查是否是直接返回的数组数据
    if (Array.isArray(res)) {
      if (res.length > 0) {
        checkPartState.options = res.map((item: any) => {
          const frequency = item.frequency || 0;
          const name = item.name || '';
          const id = item.id || '';

          const option = {
            label: name,
            value: id,
            frequency: frequency,
          };
          return option;
        });
      } else {
        checkPartState.options = [];
        message.warning('没有找到可用的部位选项');
      }
    }
    // 检查是否是包装的响应对象
    else if (res && res.success === true && res.result && Array.isArray(res.result)) {
      if (res.result.length > 0) {
        console.log('First item:', res.result[0]);

        checkPartState.options = res.result.map((item: any) => {
          const frequency = item.frequency || 0;
          const name = item.name || '';
          const id = item.id || '';

          const option = {
            label: frequency > 0 ? `${name} (${frequency}次)` : name,
            value: id,
            frequency: frequency,
          };
          return option;
        });
      } else {
        checkPartState.options = [];
        message.warning('没有找到可用的部位选项');
      }
    }
    // 处理错误情况
    else {
      checkPartState.options = [];
      if (!res) {
        message.error('网络请求失败');
      } else if (res.success === false) {
        message.error(res?.message || '接口返回失败状态');
      } else {
        message.error('接口返回数据格式不正确');
      }
    }
  } catch (error) {
    console.error('Load check parts error:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });
    message.error('加载部位选项失败: ' + (error.message || '网络错误'));
    checkPartState.options = [];
  } finally {
    checkPartState.loading = false;
  }
}

const searchCheckParts = debounce(async (keyword: string) => {
  console.log('Search check parts triggered with keyword:', keyword);
  console.log('Current item group:', checkPartState.currentItemGroup);

  if (!checkPartState.currentItemGroup) {
    console.warn('No current item group, search cancelled');
    return;
  }

  if (!keyword || keyword.trim() === '') {
    // 如果关键字为空，重新加载所有选项
    console.log('Empty keyword, loading all parts');
    await loadCheckParts(checkPartState.currentItemGroup.id);
  } else {
    // 使用关键字搜索
    console.log('Searching with keyword:', keyword.trim());
    await loadCheckParts(checkPartState.currentItemGroup.id, keyword.trim());
  }
}, 300);

function closeCheckPartSelector() {
  console.log('Closing check part selector');
  checkPartState.visible = false;
  checkPartState.currentItemGroup = null;
  checkPartState.selectedParts = [];
  checkPartState.options = [];
  checkPartState.loading = false;
}

// 处理模态框键盘事件
function handleModalKeydown(event: KeyboardEvent) {
  // Enter键保存（但不在搜索框输入时触发）
  if (event.key === 'Enter' && !event.shiftKey && event.target !== checkPartSelectRef.value?.$el?.querySelector('input')) {
    event.preventDefault();
    if (checkPartState.selectedParts.length > 0 && !checkPartState.loading) {
      confirmAddItemWithParts();
    }
  }
  // Esc键取消
  else if (event.key === 'Escape') {
    event.preventDefault();
    closeCheckPartSelector();
  }
}

// 处理选择器键盘事件
function handleSelectKeydown(event: KeyboardEvent) {
  // 在选择器中按Enter时，如果有选中项目则保存
  if (event.key === 'Enter' && event.ctrlKey && checkPartState.selectedParts.length > 0) {
    event.preventDefault();
    confirmAddItemWithParts();
  }
}

// 移除已选择的部位
function removeSelectedPart(partId: string) {
  const index = checkPartState.selectedParts.indexOf(partId);
  if (index > -1) {
    checkPartState.selectedParts.splice(index, 1);
  }
}

// 根据ID获取部位名称
function getPartNameById(partId: string): string {
  const option = checkPartState.options.find((opt) => opt.value === partId);
  return option ? option.label : partId;
}

// 格式化项目名称显示（包含部位信息）
function formatItemDisplayName(itemName: string, checkPartName?: string): string {
  if (!checkPartName) {
    return itemName;
  }
  return `${itemName}[${checkPartName}]`;
}

// 获取项目名称的完整显示文本（用于tooltip）
function getItemTooltipText(itemName: string, checkPartName?: string): string {
  return formatItemDisplayName(itemName, checkPartName);
}

// 处理套餐部位补充确认
async function handleSuitPartConfirm(itemsWithParts: ItemGroup[]) {
  console.log('Suit part selection confirmed:', itemsWithParts);

  try {
    groupLoading.value = true;
    let addList: CustomerRegItemGroup[] = [];
    let duplicateItems: string[] = [];
    let invalidItems: string[] = [];

    itemsWithParts.forEach((item) => {
      // 检查是否已存在相同的项目-部位组合（批量检查时禁用重复提示）
      const isDuplicate = checkItemPartCombinationDuplicate(regGroupDataSource.value, item.id, item.checkPartId, false);

      if (isDuplicate) {
        duplicateItems.push(`${item.name} - ${item.checkPartName}`);
        return;
      }

      const { isValid, errorMessage } = isItemGroupAvailable(item, customerReg.value);
      if (!isValid) {
        invalidItems.push(`${item.name}: ${errorMessage}`);
        return;
      }

      let data = generateCustomerRegItemGroup(item);
      if (data != null) {
        // 设置部位信息
        data.checkPartId = item.checkPartId;
        data.checkPartName = item.checkPartName;
        data.checkPartCode = item.checkPartCode;

        // 如果是从套餐添加的，设置套餐信息
        if (currentSuitInfo.value) {
          data.itemSuitName = currentSuitInfo.value.name;
          data.itemSuitId = currentSuitInfo.value.id;
        }

        addList.push(data);
        console.log(`Added item with part: ${item.name} - ${item.checkPartName}`);
      }
    });

    if (addList.length > 0) {
      const res = await addCustomerReg(addList);
      if (res.success) {
        message.success(`成功添加套餐项目 ${addList.length} 个（已跳过赠送和依赖项处理）`);
        fetchCustomerRegGroupList(selectedCustomerReg.value.id);
      } else {
        message.error(res.message || '添加失败');
      }
    } else {
      // 根据具体失败原因给出准确提示
      if (duplicateItems.length > 0 && invalidItems.length === 0) {
        message.warn(`以下项目-部位组合已存在：${duplicateItems.join('、')}`);
      } else if (invalidItems.length > 0 && duplicateItems.length === 0) {
        message.warn(`以下项目不符合添加条件：${invalidItems.join('、')}`);
      } else if (duplicateItems.length > 0 && invalidItems.length > 0) {
        message.warn(`部分项目已存在，部分项目不符合添加条件。已存在：${duplicateItems.join('、')}；不符合条件：${invalidItems.join('、')}`);
      } else {
        message.warn('没有可添加的项目');
      }
    }
  } catch (error) {
    console.error('Add suit items with parts error:', error);
    message.error('添加失败');
  } finally {
    groupLoading.value = false;
    currentSuitInfo.value = null; // 清除套餐信息
  }
}

// 处理套餐部位补充取消
function handleSuitPartCancel() {
  console.log('Suit part selection cancelled');
  groupLoading.value = false;
  currentSuitInfo.value = null; // 清除套餐信息
}

// 当前套餐信息（用于部位补充时记录套餐信息）
const currentSuitInfo = ref(null);

// 依赖检查器
const dependencyChecker = ref(null);

// 初始化依赖检查器
onMounted(() => {
  dependencyChecker.value = new DependencyChecker(regGroupDataSource);

  // 预加载常用项目的关系数据
  preloadCommonItemRelations();
});

// 预加载常用项目的关系数据
async function preloadCommonItemRelations() {
  try {
    // 获取当前客户已有的项目ID列表
    const existingItemIds = regGroupDataSource.value.filter((item) => item.addMinusFlag !== -1).map((item) => item.itemGroupId);

    // 预加载这些项目的关系数据
    if (existingItemIds.length > 0) {
      await preloadRelationData([...new Set(existingItemIds)]);
      console.log('预加载项目关系数据完成');
    }
  } catch (error) {
    console.error('预加载项目关系数据失败:', error);
  }
}

// 添加项目后的依赖检查（已迁移到后端统一分析）
async function checkDependenciesAfterAdd(addedItems) {
  console.log('ℹ️ checkDependenciesAfterAdd 被调用，但依赖关系已由后端统一分析');
  console.log('   - 新增项目数量:', addedItems.length);
  console.log('   - 当前缺失依赖数量:', missingDependencies.value?.length || 0);

  // 依赖关系数据已经在主要的数据获取函数中由后端返回
  // 无需在添加项目后重复检查，直接返回

  // 如果确实需要重新检查（比如手动添加项目），可以调用主要的数据刷新函数
  // 这样可以获取最新的完整依赖分析结果
  return;
}

// 处理依赖项目快捷添加
async function handleDependencyQuickAdd({ dependencies, originalItems }) {
  try {
    dependencyQuickAddModalRef.value?.setLoading(true);

    console.log('开始快捷添加依赖项目:', dependencies);

    // 直接通过API搜索并添加依赖项目
    const projectsToAdd = [];

    for (const dependency of dependencies) {
      try {
        // 通过项目ID直接查询项目信息
        console.log(`搜索依赖项目: ${dependency.name} (${dependency.id})`);

        // 设置搜索条件为项目名称
        const searchParams = {
          name: dependency.name,
          pageNo: 1,
          pageSize: 10,
        };

        // 调用项目搜索API
        const searchResult = await listItemGroup(searchParams);
        console.log(`搜索结果:`, searchResult);

        // 在搜索结果中查找匹配的项目
        const foundProject = searchResult.records?.find((item) => item.id === dependency.id);

        if (foundProject) {
          console.log(`找到依赖项目: ${foundProject.name}`);
          projectsToAdd.push(foundProject);
        } else {
          console.warn(`未找到依赖项目: ${dependency.name} (${dependency.id})`);
        }
      } catch (searchError) {
        console.error(`搜索依赖项目失败: ${dependency.name}`, searchError);
      }
    }

    if (projectsToAdd.length === 0) {
      message.warn('未找到可添加的依赖项目，请检查项目是否存在或手动搜索添加');
      dependencyQuickAddModalRef.value?.setLoading(false);
      return;
    }

    console.log(
      '准备添加的项目:',
      projectsToAdd.map((p) => p.name)
    );

    // 批量添加依赖项目（标记为自动添加，禁用警告提示）
    await handleAddBatch(projectsToAdd, true);

    // 关闭模态框
    dependencyQuickAddModalRef.value?.close();

    message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
  } catch (error) {
    console.error('快捷添加依赖项目失败:', error);
    message.error('快捷添加失败: ' + (error.message || '未知错误'));
    dependencyQuickAddModalRef.value?.setLoading(false);
  }
}

// 处理依赖检查确认（忽略并继续）
function handleDependencyConfirm({ dependencies, originalItems, action }) {
  console.log('用户选择忽略依赖检查并继续');
  // 这里可以记录用户的选择，或者执行其他逻辑
}

// 处理依赖检查取消
function handleDependencyCancel({ dependencies, originalItems }) {
  console.log('用户取消了依赖检查');
  // 这里可以执行取消后的清理逻辑
}

// 处理一键添加所有依赖项目（从项目列表上方的提示区域）
async function handleQuickAddAllDependencies() {
  if (missingDependencies.value.length === 0) {
    return;
  }

  try {
    addingDependencies.value = true;

    console.log('开始快捷添加依赖项目:', missingDependencies.value);

    // 直接通过API搜索并添加依赖项目
    const projectsToAdd = [];

    for (const dependency of missingDependencies.value) {
      try {
        // 通过项目ID直接查询项目信息
        console.log(`搜索依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);

        // 设置搜索条件为项目名称
        const searchParams = {
          name: dependency.dependentName,
          pageNo: 1,
          pageSize: 10,
        };

        // 调用项目搜索API
        const searchResult = await listItemGroup(searchParams);
        console.log(`搜索结果:`, searchResult);

        // 在搜索结果中查找匹配的项目
        const foundProject = searchResult.records?.find((item) => item.id === dependency.dependentId);

        if (foundProject) {
          console.log(`找到依赖项目: ${foundProject.name}`);
          projectsToAdd.push(foundProject);
        } else {
          console.warn(`未找到依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);
        }
      } catch (searchError) {
        console.error(`搜索依赖项目失败: ${dependency.dependentName}`, searchError);
      }
    }

    if (projectsToAdd.length === 0) {
      message.warn('未找到可添加的依赖项目，请检查项目是否存在');
      return;
    }

    console.log(
      '准备添加的项目:',
      projectsToAdd.map((p) => p.name)
    );

    // 批量添加依赖项目（标记为自动添加，禁用警告提示）
    await handleAddBatch(projectsToAdd, true);

    // 清空缺失依赖列表
    missingDependencies.value = [];

    message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
  } catch (error) {
    console.error('快捷添加依赖项目失败:', error);
    message.error('快捷添加失败: ' + (error.message || '未知错误'));
  } finally {
    addingDependencies.value = false;
  }
}

// 依赖关系检查（已迁移到后端统一处理）
async function checkAllDependencies(forceCheck = false) {
  console.log('ℹ️ checkAllDependencies 被调用，但依赖关系已由后端统一分析');
  console.log('   - 项目数量:', regGroupDataSource.value?.length || 0);
  console.log('   - 缺失依赖数量:', missingDependencies.value?.length || 0);

  // 依赖关系数据已经在 fetchCustomerRegGroupList 中由后端返回
  // 无需前端重复检查，直接返回
  return;

}

// 合并相同大项的依赖项目
function mergeDependenciesByGroup(dependencies) {
  const groupMap = new Map();

  dependencies.forEach((dep) => {
    const groupId = dep.dependentId;

    if (!groupMap.has(groupId)) {
      groupMap.set(groupId, {
        dependentId: groupId,
        dependentName: dep.dependentName,
        dependentType: dep.dependentType,
        relatedItems: [], // 依赖此大项的项目列表
        dependentItemDetails: [], // 具体依赖的小项列表
      });
    }

    const group = groupMap.get(groupId);

    // 添加依赖此大项的项目信息
    const itemDisplay = dep.partName ? `${dep.itemName}-${dep.partName}` : dep.itemName;
    if (!group.relatedItems.includes(itemDisplay)) {
      group.relatedItems.push(itemDisplay);
    }

    // 添加具体依赖的小项信息
    if (dep.dependentItemDetails && !group.dependentItemDetails.includes(dep.dependentItemDetails)) {
      group.dependentItemDetails.push(dep.dependentItemDetails);
    }
  });

  // 转换为数组并格式化小项信息
  return Array.from(groupMap.values()).map((group) => ({
    ...group,
    dependentItemDetails: group.dependentItemDetails.filter(Boolean).join('、') || '',
    relatedItemsText: group.relatedItems.join('、'),
  }));
}

// 项目来源分析（已迁移到后端统一处理）
async function analyzeProjectSources() {
  console.log('ℹ️ analyzeProjectSources 被调用，但项目来源已由后端统一分析');
  console.log('   - 项目来源类型数量:', itemSourceMap.value?.size || 0);

  // 项目来源数据已经在 fetchCustomerRegGroupList 中由后端返回
  // 无需前端重复分析，直接返回
  return;
}

// 建立详细的关系映射
async function buildRelationshipMaps() {
  const mainItems = regGroupDataSource.value.filter(item =>
    getItemSourceType(item.itemGroupId) === 'main'
  );

  // 清空现有映射
  itemRelationshipMap.value.clear();
  mainToChildrenMap.value.clear();

  // 为每个主项目建立关系映射
  for (const mainItem of mainItems) {
    try {
      const relations = await getItemRelations(mainItem.itemGroupId);
      const childrenIds = new Set();

      // 处理依赖项目
      if (relations.dependentGroups && relations.dependentGroups.length > 0) {
        relations.dependentGroups.forEach(dep => {
          const depId = typeof dep === 'string' ? dep : dep.relationGroupId;
          if (depId) {
            const cacheKey = `${depId}_dependent`;
            itemRelationshipMap.value.set(cacheKey, mainItem.itemGroupId);
            childrenIds.add(depId);
          }
        });
      }

      // 处理赠送项目
      if (relations.giftGroups && relations.giftGroups.length > 0) {
        relations.giftGroups.forEach(gift => {
          const giftId = typeof gift === 'string' ? gift : gift.relationGroupId;
          if (giftId) {
            const cacheKey = `${giftId}_gift`;
            itemRelationshipMap.value.set(cacheKey, mainItem.itemGroupId);
            childrenIds.add(giftId);
          }
        });
      }

      // 处理附属项目
      if (relations.attachGroups && relations.attachGroups.length > 0) {
        relations.attachGroups.forEach(attach => {
          const attachId = typeof attach === 'string' ? attach : attach.relationGroupId;
          if (attachId) {
            const cacheKey = `${attachId}_attach`;
            itemRelationshipMap.value.set(cacheKey, mainItem.itemGroupId);
            childrenIds.add(attachId);
          }
        });
      }

      // 建立主项目到子项目的映射
      if (childrenIds.size > 0) {
        mainToChildrenMap.value.set(mainItem.itemGroupId, Array.from(childrenIds));
      }

    } catch (error) {
      console.error(`建立项目关系映射失败: ${mainItem.itemGroupId}`, error);
    }
  }
}

// 获取项目的来源类型（优先使用后端返回的sourceType）
function getItemSourceType(itemGroupId) {
  // 先查找项目记录
  const item = regGroupDataSource.value.find(item => item.itemGroupId === itemGroupId);
  if (!item) {
    return 'main';
  }

  // 优先使用后端返回的sourceType（迁移到后端逻辑后的新方式）
  if (item.sourceType) {
    console.log(`项目 ${item.itemGroupName} 使用后端sourceType: ${item.sourceType}`);
    return item.sourceType;
  }

  // 降级到前端判断逻辑（兼容性保证）
  console.log(`项目 ${item.itemGroupName} 使用前端判断逻辑`);

  // 1. 检查是否有套餐来源
  if (item.itemSuitId && item.itemSuitName) {
    return 'suit'; // 来自套餐
  }

  // 2. 检查是否有赠检标志
  if (item.giveAwayFlag === '1') {
    return 'gift'; // 赠送项目
  }

  // 3. 检查是否有attachBaseId（附属项目关系）
  if (item.attachBaseId) {
    // 查找是否有其他项目的id等于当前项目的attachBaseId
    const baseItem = regGroupDataSource.value.find(base =>
      base.id === item.attachBaseId
    );
    if (baseItem) {
      return 'attach'; // 附属项目
    }
  }

  // 4. 检查是否有父项目组ID（可能表示依赖关系）
  if (item.parentGroupId) {
    // 查找是否有其他项目以此项目的parentGroupId作为itemGroupId
    const parentItem = regGroupDataSource.value.find(parent =>
      parent.itemGroupId === item.parentGroupId && parent.id !== item.id
    );
    if (parentItem) {
      return 'dependent'; // 依赖项目
    }
  }

  // 4. 通过项目名称模式识别（临时方案）
  const itemName = item.itemGroupName || '';
  if (itemName.includes('附属') || itemName.includes('配套')) {
    return 'attach'; // 附属项目
  }

  // 5. 检查是否有attachGroupIds（主项目标识）
  if (item.attachGroupIds && Array.isArray(item.attachGroupIds) && item.attachGroupIds.length > 0) {
    // 如果有attachGroupIds，说明这是一个主项目
    return 'main';
  }

  // 移除时间推测逻辑，只依赖明确的关系字段

  // 6. 回退到异步分析结果
  const asyncResult = itemSourceMap.value.get(itemGroupId);
  if (asyncResult) {
    return asyncResult;
  }

  // 默认为主项目
  return 'main';
}

// 项目关系显示模式配置
const relationDisplayMode = ref('prefix'); // 'prefix' | 'badge' | 'both' | 'none'

// 项目关系映射缓存
const itemRelationshipMap = ref(new Map());

// 主项目到子项目的映射缓存
const mainToChildrenMap = ref(new Map());

// 控制是否显示详细的badge
const showDetailedBadges = computed(() => {
  return relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'both';
});

// 获取项目关系前缀显示配置
function getItemRelationPrefix(itemGroupId) {
  console.log(`getItemRelationPrefix called for ${itemGroupId}, relationDisplayMode: ${relationDisplayMode.value}`);

  if (relationDisplayMode.value === 'badge' || relationDisplayMode.value === 'none') {
    console.log(`返回null，因为显示模式是 ${relationDisplayMode.value}`);
    return null;
  }

  const sourceType = getItemSourceType(itemGroupId);
  console.log(`项目 ${itemGroupId} 的sourceType: ${sourceType}`);

  // 提供多种前缀样式
  const prefixStyles = {
    'tree': { dependent: '├─', gift: '├─', attach: '├─', suit: '├─' },
    'arrow': { dependent: '→', gift: '→', attach: '→', suit: '→' },
    'dot': { dependent: '●', gift: '●', attach: '●', suit: '●' },
    'icon': { dependent: '🔗', gift: '🎁', attach: '📎', suit: '📦' }
  };

  const currentStyle = 'tree'; // 可以做成配置项
  const prefixText = prefixStyles[currentStyle];

  switch (sourceType) {
    case 'dependent':
      console.log(`返回依赖项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.dependent,
        color: '#fa8c16',
        title: '依赖项目：由其他项目的依赖关系自动添加'
      };
    case 'gift':
      console.log(`返回赠送项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.gift,
        color: '#52c41a',
        title: '赠送项目：由其他项目的赠送关系自动添加'
      };
    case 'attach':
      console.log(`返回附属项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.attach,
        color: '#722ed1',
        title: '附属项目：由其他项目的附属关系自动添加'
      };
    case 'suit':
      console.log(`返回套餐项目前缀 for ${itemGroupId}`);
      return {
        text: prefixText.suit,
        color: '#1890ff',
        title: '套餐项目：来自套餐配置'
      };
    default:
      console.log(`返回null，因为sourceType是 ${sourceType} for ${itemGroupId}`);
      return null;
  }
}

// 切换显示模式
function toggleRelationDisplayMode() {
  const modes = ['prefix', 'badge', 'both', 'none'];
  const currentIndex = modes.indexOf(relationDisplayMode.value);
  const nextIndex = (currentIndex + 1) % modes.length;
  relationDisplayMode.value = modes[nextIndex];

  // 保存用户偏好
  localStorage.setItem('relationDisplayMode', relationDisplayMode.value);
}

// 获取显示模式的文本描述
function getDisplayModeText() {
  const modeTexts = {
    'prefix': '前缀',
    'badge': '标签',
    'both': '全部',
    'none': '隐藏'
  };
  return modeTexts[relationDisplayMode.value] || '前缀';
}

// 获取显示模式的图标
function getDisplayModeIcon() {
  const modeIcons = {
    'prefix': h(BranchesOutlined),
    'badge': h(TagOutlined),
    'both': h(AppstoreOutlined),
    'none': h(EyeInvisibleOutlined)
  };
  return modeIcons[relationDisplayMode.value] || h(BranchesOutlined);
}

// 获取显示模式的提示信息
function getDisplayModeTooltip() {
  const modeTooltips = {
    'prefix': '前缀模式：使用 ├─ 符号显示项目关系',
    'badge': '标签模式：使用彩色标签显示项目关系',
    'both': '完整模式：同时显示前缀和标签',
    'none': '隐藏模式：不显示项目关系标识'
  };
  return modeTooltips[relationDisplayMode.value] + '（点击切换）';
}

// 初始化显示模式
onMounted(() => {
  const savedMode = localStorage.getItem('relationDisplayMode');
  if (savedMode && ['prefix', 'badge', 'both', 'none'].includes(savedMode)) {
    relationDisplayMode.value = savedMode;
  }
});

// 获取项目来源的badge配置
function getItemSourceBadge(itemGroupId) {
  const sourceType = getItemSourceType(itemGroupId);

  switch (sourceType) {
    case 'dependent':
      return {
        text: '依赖',
        bg: '#fa8c16', // 橙色 - 依赖项目，表示需要注意的关联关系
        color: '#fff',
        title: '此项目是依赖项目，由其他项目的依赖关系自动添加，点击查看详情'
      };
    case 'gift':
      return {
        text: '赠送',
        bg: '#52c41a', // 绿色 - 赠送项目，表示免费获得
        color: '#fff',
        title: '此项目是赠送项目，由其他项目的赠送关系自动添加，点击查看详情'
      };
    case 'attach':
      return {
        text: '附属',
        bg: '#722ed1', // 紫色 - 附属项目，表示从属关系
        color: '#fff',
        title: '此项目是附属项目，由其他项目的附属关系自动添加，点击查看详情'
      };
    case 'suit':
      return {
        text: '套餐',
        bg: '#1890ff', // 蓝色 - 套餐项目
        color: '#fff',
        title: '此项目来自套餐配置，点击查看详情'
      };
    default:
      return null;
  }
}

// 显示项目关系详情
async function showItemRelationDetail(itemGroupId) {
  const currentItem = regGroupDataSource.value.find(item => item.itemGroupId === itemGroupId);
  if (!currentItem) {
    message.warning('未找到项目信息');
    return;
  }

  relationDetailModal.value.visible = true;
  relationDetailModal.value.loading = true;
  relationDetailModal.value.itemInfo = currentItem;
  relationDetailModal.value.relationData = null;

  try {
    // 获取项目关系数据
    const relationData = await getItemRelations(itemGroupId);

    // 分析当前项目的来源
    const sourceType = getItemSourceType(itemGroupId);

    // 查找是哪个主项目导致了这个项目的添加
    const mainItems = regGroupDataSource.value.filter(item =>
      getItemSourceType(item.itemGroupId) === 'main' &&
      item.addMinusFlag !== -1
    );

    let sourceInfo = null;
    for (const mainItem of mainItems) {
      const mainRelations = await getItemRelations(mainItem.itemGroupId);

      // 检查是否在依赖项目中
      if (sourceType === 'dependent' && mainRelations.dependentGroups) {
        const found = mainRelations.dependentGroups.find(dep =>
          (typeof dep === 'string' ? dep : dep.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'dependent',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }

      // 检查是否在赠送项目中
      if (sourceType === 'gift' && mainRelations.giftGroups) {
        const found = mainRelations.giftGroups.find(gift =>
          (typeof gift === 'string' ? gift : gift.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'gift',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }

      // 检查是否在附属项目中
      if (sourceType === 'attach' && mainRelations.attachGroups) {
        const found = mainRelations.attachGroups.find(attach =>
          (typeof attach === 'string' ? attach : attach.relationGroupId) === itemGroupId
        );
        if (found) {
          sourceInfo = {
            type: 'attach',
            mainItem: mainItem,
            relationDetail: found
          };
          break;
        }
      }
    }

    relationDetailModal.value.relationData = {
      sourceInfo,
      allRelations: relationData
    };

  } catch (error) {
    console.error('获取项目关系详情失败:', error);
    message.error('获取项目关系详情失败');
  } finally {
    relationDetailModal.value.loading = false;
  }
}

// 关闭关系详情模态框
function closeRelationDetailModal() {
  relationDetailModal.value.visible = false;
  relationDetailModal.value.itemInfo = null;
  relationDetailModal.value.relationData = null;
}



// 检查项目是否重复 - 考虑部位信息和特殊规则
function checkItemGroupDuplicate(existingGroups: CustomerRegItemGroup[], newGroup: ItemGroup, showWarning: boolean = true): boolean {
  // 仅收费项目允许重复添加
  if (newGroup?.chargeItemOnlyFlag == '1') {
    console.log(`项目 ${newGroup.name} 是仅收费项目，允许重复添加`);
    return false;
  }

  // 分别处理减项和正常项目
  const minusItems = existingGroups.filter((item) => item.itemGroupId === newGroup.id && item.addMinusFlag == -1 && item.payStatus !== '退款成功');

  const normalItems = existingGroups.filter(
    (item) => item.itemGroupId === newGroup.id && item.addMinusFlag != -1 && item.payStatus !== '退款成功'
  );

  // 如果项目不需要部位选择
  if (newGroup.hasCheckPart !== '1') {
    // 如果存在减项，提示反减
    if (minusItems.length > 0) {
      console.log(`项目 ${newGroup.name} 存在减项记录，建议进行反减操作`);
      // 只有在需要显示警告时才弹出提示
      if (showWarning) {
        message.warning(`项目"${newGroup.name}"存在减项记录，建议进行反减操作而不是重新添加`);
      }
      return true; // 阻止添加
    }

    // 检查是否存在正常项目
    const exists = normalItems.length > 0;
    if (exists) {
      console.log(`项目 ${newGroup.name} 已存在（无需部位选择）`);
    }
    return exists;
  }

  // 如果项目需要部位选择
  // 如果新项目没有具体的部位信息，检查是否有减项
  if (!newGroup.checkPartId) {
    // 没有具体部位信息，但检查是否有减项
    if (minusItems.length > 0) {
      const minusPartNames = minusItems.map((item) => item.checkPartName || '未知部位').join('、');
      console.log(`项目 ${newGroup.name} 存在减项记录（部位：${minusPartNames}），建议进行反减操作`);

      // 添加防重复提示的机制
      const messageKey = `minus-warning-general-${newGroup.id}`;
      if (!window.lastWarningTime || !window.lastWarningTime[messageKey] ||
          Date.now() - window.lastWarningTime[messageKey] > 3000) { // 3秒内不重复提示
        window.lastWarningTime = window.lastWarningTime || {};
        window.lastWarningTime[messageKey] = Date.now();
        message.warning(`项目"${newGroup.name}"存在减项记录（部位：${minusPartNames}），建议进行反减操作而不是重新添加`);
      }
      return true;
    }

    // 没有具体部位信息，需要用户选择部位，不算重复
    return false;
  }

  // 有具体部位信息，检查特定部位组合是否重复
  return checkItemPartCombinationDuplicate(existingGroups, newGroup.id, newGroup.checkPartId, showWarning);
}

// 检查特定项目-部位组合是否重复
function checkItemPartCombinationDuplicate(existingGroups: CustomerRegItemGroup[], itemGroupId: string, checkPartId: string, showMessage: boolean = true): boolean {
  // 检查是否存在减项
  const minusItem = existingGroups.find(
    (item) => item.itemGroupId === itemGroupId && item.checkPartId === checkPartId && item.addMinusFlag == -1 && item.payStatus !== '退款成功'
  );

  if (minusItem) {
    console.log(`项目-部位组合存在减项记录，建议进行反减操作`);
    // 添加防重复提示的机制
    if (showMessage) {
      // 使用防抖机制避免重复提示
      const messageKey = `minus-warning-${itemGroupId}-${checkPartId}`;
      if (!window.lastWarningTime || !window.lastWarningTime[messageKey] ||
          Date.now() - window.lastWarningTime[messageKey] > 3000) { // 3秒内不重复提示
        window.lastWarningTime = window.lastWarningTime || {};
        window.lastWarningTime[messageKey] = Date.now();
        message.warning(`该项目-部位组合存在减项记录，建议进行反减操作而不是重新添加`);
      }
    }
    return true; // 阻止添加
  }

  // 检查是否存在正常项目
  return existingGroups.some(
    (item) => item.itemGroupId === itemGroupId && item.checkPartId === checkPartId && item.addMinusFlag != -1 && item.payStatus !== '退款成功'
  );
}

// 测试搜索功能的方法
function testCheckPartSearch() {
  if (checkPartState.currentItemGroup) {
    console.log('Testing search with keyword: 头部');
    searchCheckParts('头部');
  } else {
    console.log('No current item group for testing');
  }
}

// 测试API连接的方法
async function testApiConnection() {
  try {
    console.log('Testing API connection...');
    const testParams = { itemGroupId: 'test-id' };
    console.log('Test params:', testParams);

    const response = await listByItemGroup(testParams);
    console.log('Test API response:', response);

    if (response) {
      message.success('API连接正常');
    } else {
      message.error('API连接失败：无响应');
    }
  } catch (error) {
    console.error('API connection test failed:', error);
    message.error('API连接测试失败: ' + error.message);
  }
}

// 在组件挂载时测试API连接（仅用于调试）
onMounted(() => {
  console.log('Component mounted');
  // testApiConnection(); // 取消注释以启用API测试
});

async function confirmAddItemWithParts() {
  // 验证必要条件
  if (!checkPartState.currentItemGroup) {
    message.warn('项目信息丢失，请重新选择');
    closeCheckPartSelector();
    return;
  }

  if (checkPartState.selectedParts.length === 0) {
    message.warn('请至少选择一个检查部位');
    // 聚焦到选择器
    nextTick(() => {
      checkPartSelectRef.value?.focus();
    });
    return;
  }

  checkPartState.loading = true;
  try {
    const selectedPartNames = checkPartState.selectedParts.map((id) => getPartNameById(id));
    console.log('Adding item with parts:', {
      itemGroup: checkPartState.currentItemGroup.name,
      parts: selectedPartNames,
    });

    // 前端负责数据拼装
    const itemGroups = [];
    const parentGroupId = checkPartState.currentItemGroup.id; // 使用项目ID作为父组ID，用于关联同一项目的不同部位

    for (const partId of checkPartState.selectedParts) {
      const partName = getPartNameById(partId);
      const partInfo = checkPartState.options.find((opt) => opt.value === partId);

      // 创建临时项目对象用于重复检查
      const tempItem = {
        ...checkPartState.currentItemGroup,
        checkPartId: partId,
        checkPartName: partName,
      };

      // 使用现有的重复检查方法（会自动排除耗材）
      const isDuplicate = checkItemGroupDuplicate(regGroupDataSource.value, tempItem);
      if (isDuplicate) {
        message.warn(`${checkPartState.currentItemGroup.name} - ${partName} 已存在`);
        continue;
      }

      // 使用现有的generateCustomerRegItemGroup方法生成基础数据
      const baseData = generateCustomerRegItemGroup(checkPartState.currentItemGroup);
      if (!baseData) {
        continue;
      }

      // 设置部位相关信息
      baseData.checkPartId = partId;
      baseData.checkPartName = partName;
      baseData.checkPartCode = partInfo?.code || '';
      baseData.parentGroupId = parentGroupId;

      itemGroups.push(baseData);
    }

    if (itemGroups.length === 0) {
      message.warn('没有可添加的项目-部位组合');
      return;
    }

    // 前端互斥检查
    const mutexCheck = await checkItemMutex(itemGroups, regGroupDataSource.value);
    if (!mutexCheck.isValid) {
      const conflictMsg = formatConflictMessage(mutexCheck.conflicts);
      message.error('项目冲突：\n' + conflictMsg);
      return;
    }

    if (mutexCheck.warning) {
      message.warning(mutexCheck.warning);
    }

    const params = {
      customerRegId: customerRegId.value,
      itemGroups: itemGroups,
    };

    const res = await addItemGroupWithCheckParts(params);
    if (res.success) {
      const partNames = selectedPartNames.join('、');
      message.success(`成功添加 ${checkPartState.currentItemGroup.name} - ${partNames}`);

      // 刷新列表
      await fetchCustomerRegGroupList(selectedCustomerReg.value.id);

      // 添加项目后进行依赖检查
      await checkDependenciesAfterAdd(itemGroups);

      closeCheckPartSelector();
    } else {
      message.error(res.message || '添加失败，请重试');
    }
  } catch (error) {
    console.error('Add item with parts error:', error);

    // 处理数据库约束错误（重复项目）
    if (error.message && (error.message.includes('Duplicate') || error.message.includes('已存在') || error.message.includes('重复'))) {
      message.warning('项目可能已被其他用户添加，正在刷新数据...');
      // 自动刷新数据
      await fetchCustomerRegGroupList(selectedCustomerReg.value.id);
      closeCheckPartSelector();
    } else {
      message.error('网络错误，添加失败');
    }
  } finally {
    checkPartState.loading = false;
  }
}

onMounted(() => {
  // Apply stored styles on component mount
  if (storedTableStyle && storedTableStyle.length > 0) {
    applyStripedStyles(storedTableStyle);
  }
  handleSendItemGroup2HisFlag();
});

defineExpose({
  open,
});
</script>
<style lang="less" scoped>
/* 使用 CSS 变量设置条纹背景颜色 */
.ant-table-striped {
  --striped-odd-color: #f9f9f9; /* 默认奇数行颜色 */
  --striped-even-color: #ffffff; /* 默认偶数行颜色 */
  --font-color-odd: #000000; /* 默认奇数行字体颜色 */
  --font-color-even: #000000; /* 默认偶数行字体颜色 */
}

/* 奇数行样式 */
.ant-table-striped :deep(.odd-row) td {
  background-color: var(--striped-odd-color) !important;
  color: var(--font-color-odd) !important;
}

/* 偶数行样式 */
.ant-table-striped :deep(.even-row) td {
  background-color: var(--striped-even-color) !important;
  color: var(--font-color-even) !important;
}

.ant-table-striped :deep(.odd-row) td input,
.ant-table-striped :deep(.even-row) td input {
  background-color: inherit;
  border: 1px solid #d9d9d9 !important;
}

:deep(.ant-table-cell) {
  padding: 1px !important;
}

.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }
}
.no-select {
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none; /* Safari and older Chrome */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer and Edge */
}

/* Custom styles to reduce padding */
:deep(.ant-table-thead > tr > th),
:deep(.ant-table-tbody > tr > td) {
  padding: 4px 8px; /* Adjust these values as needed */
}

:deep(.ant-table) {
  font-size: 12px; /* Reduce font size if desired */
}

:deep(.ant-table .ant-select-selector) {
  padding: 0 4px; /* Adjust padding for select inputs inside the table */
}

:deep(.ant-table input[type='number']),
:deep(.ant-table input[type='text']) {
  padding: 0 4px;
  height: 24px; /* Adjust height if necessary */
}

:deep(.ant-table .ant-input-number) {
  padding: 0 4px;
  height: 24px;
}

:deep(.ant-table .ant-checkbox-wrapper .ant-checkbox) {
  zoom: 0.8; /* Reduce checkbox size */
}

.fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  padding: 10px 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  z-index: 100;
}
.added-item {
  color: #cf1322;
}
.removed-item {
  color: #389e0d;
  text-decoration: line-through #135200;
}

/* 优化表格行高以适应部位信息显示 */
:deep(.ant-table-tbody > tr > td) {
  padding: 8px 12px;
  vertical-align: middle;
}

/* 项目名称列的部位标签样式优化 */
:deep(.ant-tag) {
  margin: 0;
  border: 1px solid #d9d9d9;
  background: #f6f6f6;
  color: #666;
}

:deep(.ant-tag.ant-tag-processing) {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/* 依赖项目提示区域样式 */
.missing-dependencies-alert {
  .missing-dependencies-content {
    .alert-title {
      font-weight: 600;
      color: #fa8c16;
      margin-bottom: 8px;
      display: block;
    }

    .missing-projects-list {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 4px;
    }
  }

  :deep(.ant-alert-with-description) {
    padding: 12px 16px;
  }

  :deep(.ant-alert-action) {
    margin-left: 16px;
  }
}
</style>
